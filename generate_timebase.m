function [t, sine_wave, clks, clkh, digital_data_struct, analog_data_struct] = generate_timebase(params)
%GENERATE_TIMEBASE 生成固定时间步长的时间基准和信号矩阵
%   版本: v1.0 - 标准化时间索引结构体重构版本
%   
%   重构要点:
%   1. 固定时间间隔：1e-11秒(10ps，采样周期的1/1000)
%   2. 输入信号长度：15μs + 5×10ns预留空间
%   3. 结构体存储：数字18位独立存储 + 模拟9条线路独立存储
%   4. 非交叠时钟：CLKS/CLKH (0/1电平)
%   5. 时间索引与数据结构体严格对应
%
%   输入参数:
%       params - ADC参数结构体
%           fs - 采样频率 (Hz)
%           f_in - 输入正弦波频率 (Hz) 
%           Vref - 参考电压 (V)
%           Vcm - 共模电压 (V)
%           num_sine_cycles - 仿真正弦波周期数
%
%   输出参数:
%       t - 固定步长时间向量
%       sine_wave - 正弦波信号
%       clks - CLKS时钟信号 (0/1电平)
%       clkh - CLKH时钟信号 (0/1电平)
%       digital_data_struct - 数字数据结构体 (18位独立存储)
%       analog_data_struct - 模拟数据结构体 (9条线路独立存储)

    fprintf('=== 固定时间步长信号生成器 v1.0 (结构体存储) ===\n');
    
    %% 参数设置
    fs_adc = params.fs;                    % ADC采样率 
    f_clk = fs_adc;                        % 时钟频率 = 采样频率
    f_sin = params.f_in;                   % 正弦波频率
    A_sin = 1 * params.Vref;               % 正弦波振幅
    
    % 固定时间步长参数
    dt_fixed = 1e-11;                      % 10ps固定时间步长
    t_signal = 15e-6;                      % 15μs输入信号长度
    t_buffer = 5 * 10e-9;                  % 5×10ns预留空间
    t_total = t_signal + t_buffer;         % 总仿真时长
    
    % 时钟参数
    clks_width = 0.45/fs_adc;              % CLKS脉冲宽度: 45%占空比
    clks_delay = 0.05/fs_adc;              % CLKS相位延迟: 0.05ns
    clkh_width = 0.45/fs_adc;              % CLKH脉冲宽度: 45%占空比  
    clkh_delay = 0.55/fs_adc;              % CLKH相位延迟: 0.55ns (确保非交叠)

    %% 生成固定步长时间向量
    t = 0:dt_fixed:t_total;
    num_samples = length(t);
    
    fprintf('  总时间点数: %d\n', num_samples);
    fprintf('  时钟频率: %.1f MHz\n', f_clk/1e6);
    
    %% 生成正弦波信号
    sine_wave = A_sin * sin(2*pi*f_sin*t);
    
    %% 生成非交叠时钟信号 (0/1电平)
    % 初始化时钟信号
    clks = zeros(size(t));
    clkh = zeros(size(t));
    
    num_clk_cycles = ceil(t_total * f_clk) + 2;
    
    % 生成CLKS时钟
    for n = 0:num_clk_cycles
        rise_time = n/f_clk + clks_delay;
        fall_time = rise_time + clks_width;
        
        if rise_time >= 0 && rise_time <= t_total
            rise_idx = find(t >= rise_time, 1, 'first');
            fall_idx = find(t >= fall_time, 1, 'first');
            
            if ~isempty(rise_idx) && ~isempty(fall_idx) && fall_idx <= length(t)
                clks(rise_idx:fall_idx) = 1;
            end
        end
    end
    
    % 生成CLKH时钟  
    for n = 0:num_clk_cycles
        rise_time = n/f_clk + clkh_delay;
        fall_time = rise_time + clkh_width;
        
        if rise_time >= 0 && rise_time <= t_total
            rise_idx = find(t >= rise_time, 1, 'first');
            fall_idx = find(t >= fall_time, 1, 'first');
            
            if ~isempty(rise_idx) && ~isempty(fall_idx) && fall_idx <= length(t)
                clkh(rise_idx:fall_idx) = 1;
            end
        end
    end
    
    %% 创建数字数据结构体（18位原始数字信号独立存储）
    % 创建基础数字数据结构体
    digital_data_struct = struct();
    
    % 添加时间向量 (列向量)
    digital_data_struct.time = t(:);
    
    % 初始化18位数字信号结构体数组
    digital_signals = [];
    
    % 为18位数字数据创建独立的信号结构体
    for bit_idx = 1:18
        digital_signal = struct();
        digital_signal.values = zeros(num_samples, 1);           % 初始化为零（列向量）
        digital_signal.dimensions = 1;                           % 单维信号
        digital_signal.label = sprintf('Digital_Bit_%02d', bit_idx);  % 位标签
        digital_signal.blockName = sprintf('Pipeline/Stage_%d/Bit_%d', ...
                                           ceil(bit_idx/2), mod(bit_idx-1,2)+1);  % 模块路径
        
        if isempty(digital_signals)
            digital_signals = digital_signal;
        else
            digital_signals(end+1) = digital_signal;
        end
    end
    
    digital_data_struct.signals = digital_signals;
    
    %% 创建模拟数据结构体（9条模拟数据线独立存储）
    % 创建基础模拟数据结构体
    analog_data_struct = struct();
    
    % 添加时间向量 (列向量)
    analog_data_struct.time = t(:);
    
    % 初始化9条模拟数据线结构体数组
    analog_signals = [];
    
    % 模拟数据线标签定义
    analog_labels = {'SHA_Output', 'Stage1_Output', 'Stage2_Output', 'Stage3_Output', ...
                     'Stage4_Output', 'Stage5_Output', 'Stage6_Output', 'Stage7_Output', 'Stage8_Output'};
    
    % 为9条模拟数据线创建独立的信号结构体
    for line_idx = 1:9
        analog_signal = struct();
        analog_signal.values = zeros(num_samples, 1);           % 初始化为零（列向量）
        analog_signal.dimensions = 1;                           % 单维信号
        analog_signal.label = analog_labels{line_idx};          % 数据线标签
        analog_signal.blockName = sprintf('Pipeline/%s', analog_labels{line_idx});  % 模块路径
        
        if isempty(analog_signals)
            analog_signals = analog_signal;
        else
            analog_signals(end+1) = analog_signal;
        end
    end
    
    analog_data_struct.signals = analog_signals;
    
    %% 确保输出格式一致性（列向量）
    t = t(:);
    sine_wave = sine_wave(:);
    clks = clks(:);
    clkh = clkh(:);
    
end 
