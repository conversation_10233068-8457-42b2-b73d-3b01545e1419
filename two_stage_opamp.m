fclose all;
clear;
clc;

global pathsp moslib Lb Ub gmid 
global L Cc Vdd Vss CL Av ts ovsht  Vinmax Vinmin vsd6 vds7 VTn Cox icompen

% HSPICE位置及PDK
pathsp='D:\AnalogIC\HSPICE\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe';
moslib='.LIB ''ms018_v1p9.lib'' TT';

kBOLTSMAN=1.381e-23; Temp=273+25; gamman=1;
epislon=3.9*8.854e-12; 
un=275.5555875e-4; up=116.6094811e-4; 
toxn=3.74e-9; toxp=toxn;  %0.18um
UnCox=1e6*un*epislon/toxn; UpCox=1e6*up*epislon/toxp;
Cox=epislon/toxp;
VTn=0.46; VTp=-0.458; % 0.18um  

fp=fopen('gm_id.txt','r+');
if (fp<0)
    L=1; W=5; 
    k=0; 
    for vgsk=0.3:0.05:1.5
        k=k+1;
        vdsk=vgsk; vbs=0;
        [idsk,gmk,gdsk,cgsk]=eval_bsim(-vgsk,-vdsk,vbs,W,L,1);
        idp(k)=-idsk;
        gmidp(k)=-gmk/idsk; 
        gdsidp(k)=-gdsk/idsk; 
        cgsp(k)=-cgsk/idsk;
        
        [idsk,gmk,gdsk,cgsk]=eval_bsim(vgsk,vdsk,vbs,W,L,-1);
        idn(k)=idsk;
        gmidn(k)=gmk/idsk;
        gdsidn(k)=gdsk/idsk;
        cgsn(k)=cgsk/idsk;
    end  
    gmid=[(0.3:0.05:1.5)' gmidn' gmidp' idn' idp' gdsidn' gdsidp' cgsn' cgsp']; 
    % 'Vgs'gmidn'gmidp'idn'idp'gdsidn'gdsidp'cgsn'cgsp'
    save gm_id.txt gmid -ascii;
    figure; plot(gmid(:,1),[gmid(:,2) gmid(:,3)]);
else
    fclose(fp);
    load gm_id.txt -ascii;
    gmid=gm_id;
end
% Performance Specifications获取gmid数据?
% vbs = 0;
% fp=fopen('gm_id.txt','r');
% % % if (fp<0)
% W=5; 
% k=0; 
% for vgsk=0.12:0.05:1.5
% k=k+1;
% vdsk=vgsk; 
% [idsk,gmk,gdsk,cgsk]=eval_bsim(-vgsk,-vdsk,vbs,W,L,1);
% gmidp(k)=-gmk/idsk; 
% gdsidp(k)=-gdsk/idsk; idp(k)=-idsk;
% cgsp(k)=-cgsk/idsk;
% [idsk,gmk,gdsk,cgsk]=eval_bsim(vgsk,vdsk,vbs,W,L,-1);
% gmidn(k)=gmk/idsk;
% gdsidn(k)=gdsk/idsk; idn(k)=idsk;
% cgsn(k)=cgsk/idsk;
% end 
% gmid=[(0.12:0.05:1.5)' gmidn' idn' gmidp' idp' gdsidn' gdsidp' cgsn' cgsp'];
% save gm_id.txt gmid -ascii;
% figure; plot(gmid(:,1),[gmid(:,2) gmid(:,4)]);
% load gm_id.txt -ascii;
% gmid=gm_id;
% fclose(fp);


Vinmax=1.4; Vinmin=0.4;   %0.18um             %输入范围
Av    = 80; 
% Sn    = 6.5e-9;   %Sn=1.8e-8;  % V/root Hz    %Sn:等效输入热噪声
% ovsht = 1e-2;     %peak overshoot             %尖峰过冲电压
ts    = 4e-9;       %0.1 Settling time          %建立时间
err   = 0.001;                                  % 0.1%建立精度
UGB   = 700e6;                                  %单位增益带宽600MHz
SR    = 1000e6;                                 %目标压摆率, 转换时间1ns
% %
% Tend=0.1e-6; h=0.001e-9;                        %Tend:优化目标
% t=0:h:Tend; 
% tol = 0.02;
% %电路优化，暂时保留
% %
% Vov1=0.12;      %M1 overdrive voltage
% u0=1;
% VSR = (Cs/(Cs+Ci))*(G0*u0-Vov1);
% SR0 = (VSR-G0*u0*(1-a))/(0.8*ts);     整体的压摆率，下边用两级运放的代替

% 运放设计过程
% 单位换算
UGB = UGB*1e-6;
SR = SR*1e-6;

VTnmin=VTn; VTpmax=-VTp;
L = 1;
CL = 0.4;   %所有电容不带单位

Vdd=1.8; Vss=0;  Lb=0.18; Ub=100;  %0.18um
Vout0=0.5*(Vdd+Vss);
Vin1=Vout0; Vin2=Vout0;
vbs = 0;

Cc    = 1;        %米勒电容Cc＞0.22CL
Ibias = SR*Cc*1e-6; %尾管M0偏置电流I0
ids1  = 0.5*Ibias;
K     = 4/(1+(pi/log(err))^2); % 根据相位裕度确定分离因子K:PM = atan(K)*180/pi;
PM    = rad2deg(atan(K));      % 相位裕度73°对应建立精度0.1%

gm1   = UGB*Cc*1e-6;
% 第二级放大管
% syms gm1;
% equ   = (Sn == 2*(4*kBOLTSMAN*Temp*gamman/gm1)*(1+gm3/gm1));
% gm1   = solve(equ);
% Cc    = gm1*1e6/UGB;
vsd6  = Vdd-Vout0; vds7 = Vout0-Vss;
ids6  = SR*(Cc+CL)*1e-6;
gm6   = 2.2*gm1*(CL/Cc);
icompen = 1;
if (icompen==1)    % zero nulling compensation schme %K=ω2/UGB=tan(PM)
    gm6=K*UGB*CL*1e-6;
    gmid6 = gm6/ids6;
    if (gm6/ids6>9) 
        ids6=gm6/9; end
else    % pz cancellation compensation
    omegat6=K*UGB*(1+CL/Cc);   %Mega rad/s
    %if (omegat6<4e3) continue; end
    if (omegat6<4e3) 
        icompen=1;  gm6=K*UGB*CL*1e-6; 
    else
        gmid6=interp1(gmid(:,3)./gmid(:,9),gmid(:,3),omegat6*1e6);% (gmid/cgs)
        gm6=gmid6*ids6;
    end
end
[vgs6,W6] = inv_bsim1(-ids6,gm6,-vsd6,0,L,1);
% 第一级
vcmfb = 1.2;    %M3.M4共模反馈偏置
vsg3  = Vdd-vcmfb; vsd3=-vgs6;
vgs1  = (0.1+VTn);
Vs1   = Vin1-vgs1; 
vds1  = Vdd-vsd3-Vs1;
W3    = inv_bsim(-ids1,-vsg3,-vsd3,0,L,1);
% [ids3,gm3,gds3] = eval_bsim(-vsg3,-vsd3,0,W3,L,1); %求gm3
% gm1   = UGB*Cc*1e-6;
gmid1 = gm1/ids1;
[vgs1, W1] = inv_bsim1(ids1,gm1,vds1,0,L,-1);
% vgs1  = (0.1+VTn); Vs1 = Vin1-vgs1; vds1 = Vdd-vsd3-Vs1; vgs1a = 1.1*vgs1;
% while abs(vgs1/vgs1a-1)>0.5e-2
%     [vgs1, W1] = inv_bsim1(Ibias/2,gm1,vds1,0,L,-1);
%     Vs1 = Vin1-vgs1; vds1 = Vdd-vsd3-Vs1; vgs1a = vgs1;
% end
% 不清楚这个while有什么用
% 尾管与第二级负载处在相同偏置电压
vds0  = Vs1-Vss;
Vov0  = Vinmin-Vss-vgs1; 
vgs0  = Vov0+VTn;
W0    = inv_bsim(Ibias,vgs0,vds0,0,L,-1);
vgs7  = vgs0;
W7    = inv_bsim(ids6,vgs7,vds7,0,L,-1);
if (icompen==1) 
    Rc=1/gm6; 
else 
    Rc=(1+CL/Cc)/gm6; 
end
% M14为电流偏置
vgs14 = vgs0;
vds14 = vgs0;
W14   = inv_bsim(Ibias,vgs14,vds14,0,L,-1);
% 共模反馈
ids9  = 2*Ibias; %Ids9=2*Ibias
vgs9  = vgs0;
vds9  = 0.4; %原本的vds9=vds0偏小
W9    = inv_bsim(ids9,vgs9,vds9,vbs,L,-1);
vgs10 = Vout0-Vov0;
vds10 = vcmfb-vds9;
W10   = inv_bsim(ids9/2,vgs10,vds10,vbs,L,-1);
vsg12 = vsg3;
vsd12 = vsg12;
W12   = inv_bsim(-ids9/2,-vsg12,-vsd12,vbs,L,1);
%仿真
pf    = eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,Cc,Ibias,Vdd,Vss,CL,Rc);