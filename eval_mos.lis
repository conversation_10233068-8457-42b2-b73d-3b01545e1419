 ****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
  Copyright (c) 1986 - 2024 by Synopsys, Inc. All Rights Reserved.              
  This software and the associated documentation are proprietary
  to Synopsys, Inc. This software may only be used in accordance
  with the terms and conditions of a written license agreement with
  Synopsys, Inc. All other use, reproduction, or distribution of
  this software is strictly prohibited.
  Input File: eval_mos.sp                                                       
  Command line options: D:\AnalogIC\HSPICE\Hspice_P-2019.06-SP1-1\WIN64\hspice.com -i eval_mos.sp -o eval_mos
  Start time: Tue Nov 19 16:10:55 2024
  lic:  
  lic: FLEXlm: SDK_12.3 
  lic: USER:   Junyang Dai          HOSTNAME: DESKTOP-JG002VR 
  lic: HOSTID: "000000000000"       PID:      10528 
  lic: Using FLEXlm license file: 
  lic: 27000@DESKTOP-JG002VR 
  lic: Checkout 1 hspice 
  lic: License/Maintenance for hspice will expire on 30-dec-2030/2030.12 
  lic: 1(in_use)/99(total) FLOATING license(s) on SERVER 27000@DESKTOP-JG002VR 
  lic:   
  **warning** (ms018_v1p9.mdl:142) Model p18 device geometries will not be checked against the limits set by lmin, lmax, wmin and wmax. To enable this check, add a period(.) to the model name(i.e. enable model selector).
  **error** (eval_mos.sp:5) Undefined parameter or function definition "nan" for vgss. Please enter a defined name.

          ***** job aborted
1****** HSPICE -- P-2019.06-SP1-1 win64 (Sep 27 2019) ******                    
 ******  
 nmos

 ****** job statistics summary tnom=  25.000 temp=  25.000 ******

  ******  HSPICE Threads Information  ******

  Command Line Threads Count :     1
  Available CPU Count        :    12
  Actual Threads Count       :     1


  ******  Circuit Statistics  ******
  # nodes       =       0 # elements   =       3
  # resistors   =       0 # capacitors =       0 # inductors   =       0
  # mutual_inds =       0 # vccs       =       0 # vcvs        =       0
  # cccs        =       0 # ccvs       =       0 # volt_srcs   =       2
  # curr_srcs   =       0 # diodes     =       0 # bjts        =       0
  # jfets       =       0 # mosfets    =       1 # U elements  =       0
  # T elements  =       0 # W elements =       0 # B elements  =       0
  # S elements  =       0 # P elements =       0 # va device   =       0
  # vector_srcs =       0 # N elements =       0


  ******  Runtime Statistics (seconds)  ******

  analysis           time    # points   tot. iter  conv.iter
  op point           0.00           1           0
  readin             0.01
  errchk             0.00
  setup              0.00
  output             0.00


           peak memory used         62.40 megabytes
           total cpu time            0.01 seconds
           total elapsed time        0.03 seconds
           job started at     16:10:55 11/19/2024
           job ended   at     16:10:55 11/19/2024
           job total runtime         0.03 seconds


  lic: Release hspice token(s) 
 lic: total license checkout elapse time:        0.01(s)
