<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=8" /> 
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<title>Code Generation Advisor Report for 'pipelined_TOP'</title>  
<style type="text/css">
<!--
@media screen {    
    /* Table Formatting */
    .AdvTable th { 
        background:#80a0c1 url(data:image/gif;base64,R0lGODlhAQAaAKIAAHSRr3mXtn+fv2V/mX2cvG2JpVxzi4CgwSH5BAAAAAAALAAAAAABABoAAAMJeLrcLCSAMkwCADs=) repeat-x bottom left; 
    }
}

@media all {
    *{ font-family: sans-serif; }

	H3 {
		font-size: 14pt;
		font-weight: 200;
	}
	H4 {
		font-size: 9pt;
		font-weight: normal;
	}
	H5 {
		font-size: 12pt;
		font-style: italic;
		font-weight: bold;
		color: #333333;
		margin-bottom: 2px;
	}
	a[href] {
		color: #005FCE;
	}
	.subsection {
		padding-left: 30px;
	}
    
    .CheckHeading {
		font-size:1.05em;
		font-weight:bold;
	}

    /* Table Formatting */
    table.AdvTable { 
        border-collapse:collapse; border:1px solid #ececec; border-right:none; border-bottom:none; 
    }

    .AdvTable th { 
        padding-left:5px; 
        padding-right:5px; 
        color:#fff; 
        line-height:120%; 
        background:#80a0c1 url(data:image/gif;base64,R0lGODlhAQAaAKIAAHSRr3mXtn+fv2V/mX2cvG2JpVxzi4CgwSH5BAAAAAAALAAAAAABABoAAAMJeLrcLCSAMkwCADs=) repeat-x bottom left; 
        border-right: 1px solid #ececec; 
        border-bottom: 1px solid #ececec; 
    }

    .AdvTable td { 
        padding-left:5px; 
        padding-right:5px; 
        border-right:1px solid #ececec; 
        border-bottom: 1px solid #ececec; 
    }
    
    .AdvTable th p { 
        margin-bottom:0px; 
    }

    .AdvTable p { 
        margin-bottom:10px; 
    }
    
    .AdvTableNoBorder p { 
        margin-bottom:10px; 
    }

    table+span.SDCollapseControl { 
        font-size:0.8em; 
        font-weight:bold; 
    }

    ul+span.SDCollapseControl { 
        margin-left:25px; 
        font-size:0.8em;
        font-weight:bold; 
    }

    ol+span.SDCollapseControl { 
        margin-left:25px; 
        font-size:0.8em; 
        font-weight:bold; 
    }

    .SystemdefinedCollapse { 
        margin-top:0px;
        margin-bottom:5px; 
    }

    div.AllCollapse p, div.AllCollapse table, div.AllCollapse ol, div.AllCollapse ul { 
        margin-top:0pt;
        margin-bottom:0pt; 
        margin-left:18px;
    }

    div.AllCollapse + div { 
        margin-top:0pt;
        margin-bottom:0pt; 
        margin-left:18px; 
    }

    img.CollapseAllControlImage { 
        float:left; 
    }

    .SubResultsSummary {
        padding-left:30px;
    }

    .EmptyFolderMessage {
        color:gray;
        margin:10px 0 0 30px;
        font-size:0.8em;
    }
	
    .CsEml-Symbol-Status-Pass
    { 
        color: green;
        font-family: monospace;
    }

    .CsEml-Symbol-Status-Warn
        { 
        color: orange;
        font-family: monospace;
    }

    .CsEml-Symbol-Status-Fail
    { 
        color: red;
        font-family: monospace;
    }
    
    .CsEml-Line-Number
    {
        color: #A0A0A0;
        font-style: italic;
        font-family: monospace;
    }

    .CsEml-Code
    {
        color: blue;
        font-family: monospace;
    }

    .CsEml-Code-Fragment
    {
        color: blue;
        font-weight: bold;
        font-family: monospace;
        margin-right: 0;
        padding-right: 0;
        margin-left: 0;
        padding-left: 0;
    }

    .CsEml-Function-Type
    {
        color:       #A0A0A0;
        font-style:  italic;
        font-family: monospace;
    }

}
-->
</style>

<script type="text/javascript"> <!-- /* Copyright 2013-2017 The MathWorks, Inc */
/* define String.trim() method if not defined (used by filterByText() function) */
if(!String.prototype.trim) {
  String.prototype.trim = function () {
    return this.replace(/^\s+|\s+$/g,'');
  };
}

// rtwreport needs it 
function init() {
    var showFailed = document.getElementById("Failed Checkbox");
    var showPassed = document.getElementById("Passed Checkbox");
    var showWarning = document.getElementById("Warning Checkbox");
    var showNotRun = document.getElementById("Not Run Checkbox");
    var showJustified = document.getElementById("Justified Checkbox");
    var showIncomplete = document.getElementById("Incomplete Checkbox");      
    
    urlQueryStringRegexp = RegExp('\\?(.*)').exec(window.location.search);

    if (urlQueryStringRegexp == null) {
        /* refresh check boxes and search input */
        showFailed.checked = true;
        showPassed.checked = true;
        showWarning.checked = true;
        showNotRun.checked = true;
        showJustified.checked = true;
        showIncomplete.checked = true;
    }
    else 
    {
        showFailed.checked = false;
        showPassed.checked = false;
        showWarning.checked = false;
        showNotRun.checked = false;
        
        urlQueryString = urlQueryStringRegexp[1];
        
        var queryArgs = [];
        
        if (urlQueryString.indexOf("&") == -1)
        {
            // Only 1 argument
            queryArgs[0] = urlQueryString;
        }
        else
        {
            queryArgs = urlQueryString.split("&");
        }
        
        for (var idx = 0; idx < queryArgs.length; ++idx)
        {
            // get in
            if (queryArgs[idx].localeCompare("showPassedChecks") == 0) 
            {
                showPassed.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showWarningChecks") == 0) 
            {
                showWarning.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showFailedChecks") == 0)
            {
                showFailed.checked = true;
            }
            else if (queryArgs[idx].localeCompare("showNotRunChecks") == 0)
            {
                showNotRun.checked = true;
            }
        }
            
        // if nothing is selected, select everything
        if (!showFailed.checked && !showPassed.checked && 
            !showWarning.checked && !showNotRun.checked) 
        {
            showFailed.checked = true;
            showPassed.checked = true;
            showWarning.checked = true;
            showNotRun.checked = true;
            showJustified.checked = true;
            showIncomplete.checked = true;
        }
    }
    
    updateVisibleChecks();
}

function markEmptyFolders(){
	var nodeTypes = ["FailedCheck","PassedCheck",  "WarningCheck", "NotRunCheck"];
	var folderArray = document.querySelectorAll("div.FolderContent");
	
	for (var n=0;n<folderArray.length;n++){
		/* get direct check result children and check visibility */
		var childNodes = folderArray[n].childNodes;
		var noneVisible = true;
		var noChecksInFolder = true;
		
		for (var ni=0;ni<childNodes.length;ni++){
			if (childNodes[ni].nodeType == 1 && childNodes[ni].tagName.toLowerCase() == "div"){
				if (childNodes[ni].className == nodeTypes[0] || childNodes[ni].className == nodeTypes[1] || childNodes[ni].className == nodeTypes[2] || childNodes[ni].className == nodeTypes[3]){
					noChecksInFolder = false;
					if (childNodes[ni].style.display != "none"){
						noneVisible = false;
                        break;
					}
				}
			}
		}
		
		/* Only display hidden message if any checks inside the folders and not just other folders */
		if (!noChecksInFolder){
			var hiddenMessage = folderArray[n].querySelector("div.EmptyFolderMessage");
			
			if (hiddenMessage && noneVisible == true){
				hiddenMessage.style.display = "";
			}else if (hiddenMessage && noneVisible == false){
				hiddenMessage.style.display = "none";
			}else{
				/* hidden message not found */
			}
		}
	}

    return;
}

function updateVisibleChecks( /* show all flags */ checkbox ){
	
	var checkboxes = ["Failed Checkbox", "Passed Checkbox", "Warning Checkbox", "Not Run Checkbox", "Justified Checkbox", "Incomplete Checkbox"];
	var nodeTypes = ["Failed Check","Passed Check",  "Warning Check", "Not Run Check", "Justified Check", "Incomplete Check"];
	var textInput = document.getElementById("TxtFilterInput");
	
	
	if (checkbox && textInput && textInput.color=="gray"){
		var checkType = checkbox.id;
		var ind = checkboxes.indexOf(checkType);
		var nodes = document.getElementsByName(nodeTypes[ind]);
		for (i = 0; i < nodes.length;i++){
		    nodes[i].style.display = checkbox.checked ? "" : "none";
		}
	}
	else{ /* Update all checks if called from init or if a previous text filter was applied */
		for (i = 0; i < checkboxes.length; i++){
			
			  var show = document.getElementById(checkboxes[i]).checked ? "" : "none";
			  var nodes = document.getElementsByName(nodeTypes[i]);
			  for(j = 0; j < nodes.length; j++){
				  nodes[j].style.display = show;
			  }
		   
		}
	}

    /* clear text search */
	if (textInput && checkbox){
		textInput.value = "按检查名称搜索";
        textInput.style.color = "gray";
        textInput.blur;
	}

    markEmptyFolders();

    return; 
}

function filterByText(ev){
	// get all the nodes
	var allNodeTypes = ["Failed Check","Passed Check",  "Warning Check", "Not Run Check"];
	var checkboxes = ["Failed Checkbox", "Passed Checkbox", "Warning Checkbox", "Not Run Checkbox"];
	var nodeTypes = [];
	
	// get nodes depending on filter selections
	for (var n=0; n<checkboxes.length; n++){
		var checkbox = document.getElementById(checkboxes[n]);
		if (checkbox && checkbox.checked){
			nodeTypes.push(allNodeTypes[n]);
		}
	}
	
    var searchNodes = [".CheckHeading"];
    var allnodes = [];
	var alltext = [];
	if (!ev) return;
	var target = ev.target ? ev.target : window.event.srcElement;
	var searchString = target.value;
	
	if (!searchString){
        updateVisibleChecks();  // clear all and display by other filters
	}else{
		for (i = 0; i < nodeTypes.length; i++){
			var nodes = document.getElementsByName(nodeTypes[i]);
			for (j = 0; j < nodes.length; j++){
				// get text from check heading
				var checkContent = nodes[j].querySelector(searchNodes).innerHTML;
				// creaet a regular expression to ignore case
				var ss = new RegExp(searchString.trim(), "i");
				if (ss.exec(checkContent)){
				   nodes[j].style.display = "";
				}else{
				   nodes[j].style.display = "none";
				}
			}
		}
        markEmptyFolders();
	}	
}


function MATableShrink(o,tagNameStr){
    var temp = document.getElementsByName(tagNameStr);
    var classUsed = document.getElementsByName('EmbedImages'); 
    var embeddedMode = !(classUsed.length == 0);
    var img = o.querySelector("img");

    if (temp[0].style.display == "") 
    {
        temp[0].style.display = "none";
        if (embeddedMode)
        {
            img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAkUlEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAEfQCCwt+JQABRHEYAAQQCzE2w9h//vzDUAcQQDgNgCkGacamEQYAAohiLwAEEEED8NkOAgABxEJMVOEDAAHESGlmAgggisMAIIAoNgAgwAC+/BqtC+40NQAAAABJRU5ErkJggg==";
        }
        else
        {
            img.src = "plus.png";
        }
    } 
    else 
    {
        temp[0].style.display = "";
        if (embeddedMode)
        {
            img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAhklEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAUewFgACi2ACAAGLBKcGCafafP/8wxAACCKcB2BRjAwABRLEXAAKIYgMAAoiFmKjCBwACiJHSzAQQQBR7ASCAKDYAIMAAUtQUow+YsTsAAAAASUVORK5CYII=";
        }
        else
        {
            img.src = "minus.png";
        }
    }
}

// rtwreport needs it 
function updateHyperlink() {
    docObj = window.document;
    loc = document.location;
    var aHash = "";
    var externalweb = "false";
    if (loc.search || loc.hash) {
        if (loc.search)
            aHash = loc.search.substring(1);
        else
            aHash = loc.hash.substring(1);
    }    
    var args = new Array();
    args = aHash.split('&');
    for (var i = 0; i < args.length; ++i) {
        var arg = args[i];
          sep = arg.indexOf('=');
        if (sep != -1) {
            var cmd = arg.substring(0,sep);
            var opt = arg.substring(sep+1);
            switch (cmd.toLowerCase()) {
            case "externalweb":
                externalweb = opt;
                break;
            }
        }
    }        
    if (externalweb === "true") {        
        var objs = docObj.getElementsByTagName("a");
        if (objs.length > 0 && objs[0].removeAttribute) {
            for (var i = 0; i < objs.length; ++i) {           
                if (objs[i].href.indexOf('matlab') > -1)           
                    objs[i].removeAttribute("href");                
            }
        }
    }
    init();
}
    
function findParentNode(e) {
   var parent = e.parentElement;
   if (!!parent) {
    if (parent.id == "") {   
       return findParentNode(parent);
    } else {
       return parent;
    }
   } else {
    return null;
   }
}

function expandParentNode(e) {
   var parent = findParentNode(e);
   while (!!parent) { 
    var prefix = "FolderControl_";
    var folderControl = document.getElementById(prefix.concat(parent.id));
    if (parent.style.display == "none") {       
       MATableShrink(folderControl,parent.id);
    }
    parent = findParentNode(parent);
   }
}

function isHidden(e) {
    return (e.offsetParent === null)
}

function navigateToElement(eleID) {
   var e = document.getElementById(eleID);
   if (isHidden(e)) {
       expandParentNode(e);
   }       
   if (!!e && e.scrollIntoView) {
       e.scrollIntoView();
   }
}

function setTextContent(element, value) {
    var content = element.textContent;
    if (value === undefined) return;
    
    if (content !== undefined) element.textContent = value;
    else element.innerText = value;
}

function hideControlPanel(control){
	var panelComponents = ["ControlsCheckFiltering", "ControlsView", "ControlsTOC"];
	var reportContent = document.querySelector(".ReportContent");
	var controlPanel = document.getElementById("ControlPanel");
	var isHidden = false;
	
    if (reportContent && control && controlPanel) {
    	for (var n=0; n<panelComponents.length; n++){
			var component = document.getElementById(panelComponents[n]);
			
			if (component && component.style.display == ""){
				component.style.display = "none";
            } else if (component && component.style.display == "none"){
				component.style.display = "";
			}
		}
		
		if (controlPanel.style.width != "6px"){
			reportContent.style.marginLeft = "25px";
	    	controlPanel.style.width = "6px";
	    	control.style.left = "0px";
	       	var innerDiv = control.querySelector("#HidePanelControlInner");
	       	setTextContent(innerDiv, "\u25BA");
	    } else {
	    	reportContent.style.marginLeft = "225px";
        	controlPanel.style.width = "210px";
        	control.style.left = "204px";
        	var innerDiv = control.querySelector("#HidePanelControlInner");
        	setTextContent(innerDiv, "\u25C0");
        }
    }
}
/* Copyright 2013 The MathWorks, Inc. */
//COLLAPSIBLE FEATURE

// global variables
var GlobalCollapseState = "off";

function collapseSDHelper(o, CollElementParent, disp, mode){
    var CollElement = CollElementParent; /* ul/ol with lists, tbody with table */

    if (CollElement.nodeName == "UL" || CollElement.nodeName == "OL"){
        var CollName = "LI";
    }else if (CollElement.nodeName == "TABLE"){
        if (CollElement.querySelector('tbody')) {
            /* tr modes are child nodes of tbody node */
            CollElement = CollElement.querySelector('tbody');
        }
        var CollName = "TR";
    }
    
    // get children (li for list and tr for table)
    var children = CollElement.childNodes;

    var nElements = 0;
    for (var i=0;i<children.length;i++){
        if (children[i].nodeName == CollName){
            nElements = nElements + 1;
            if (nElements > 5){
                children[i].style.display = disp;
            }
        }
    }
    if (disp == "none"){
        if (CollName == "LI"){
            var text = " 项目)";
        }else{
            var text = " 行)";
        }
        
        var textNode = document.createTextNode(("\u2228 更多 (" + (nElements-5) + text));
        o.replaceChild(textNode,o.firstChild);

        CollElementParent.setAttribute("dataCollapse", "on");

        /* scroll to element if it is not visible */
        if (mode == "single" && CollElement.offsetTop < document.documentElement.scrollTop){
			CollElement.scrollIntoView();
		}
    }else{
        var textNode = document.createTextNode(("\u2227 " + "更少"));
        o.replaceChild(textNode,o.firstChild);

        CollElementParent.setAttribute("dataCollapse", "off");
    }
}

function collapseSD(o, ID){
    var CollElement = document.getElementById(ID);
    if (CollElement != null){
        if (CollElement.getAttribute("dataCollapse") == "off"){
            var disp="none";
        }else{
            var disp="";
        }
        collapseSDHelper(o, CollElement, disp, "single");
    }
}

function collapseAllHelper(o, CollElement, CollInfo, disp){

    if (CollElement != null){
        var img = o.querySelector("img");
        if (disp == "none"){
            CollElement.style.display = disp;
			img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAkUlEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAEfQCCwt+JQABRHEYAAQQCzE2w9h//vzDUAcQQDgNgCkGacamEQYAAohiLwAEEEED8NkOAgABxEJMVOEDAAHESGlmAgggisMAIIAoNgAgwAC+/BqtC+40NQAAAABJRU5ErkJggg==";
        }else{
            CollElement.style.display = disp;
			img.src = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAAAhklEQVR42mL8//8/AyUAIICYGCgEAAFEsQEAAUSxAQABxIIu0NTURDBQ6urqGGFsgABiwaagpqYOp+aWliYUPkAAUewFgACi2ACAAGLBKcGCafafP/8wxAACCKcB2BRjAwABRLEXAAKIYgMAAoiFmKjCBwACiJHSzAQQQBR7ASCAKDYAIMAAUtQUow+YsTsAAAAASUVORK5CYII=";
        }

        if (CollInfo != null){
            if (disp == "none"){
                CollInfo.style.display = "";
            }else{
                CollInfo.style.display = "none";
            }
        }
    }
}

function collapseAll(o, ID, ID2){
    var CollElement = document.getElementById(ID);
    var CollInfo = document.getElementById(ID2);

    if (CollElement.style.display == ""){
        var disp = "none";
    }else{
        var disp = "";
    }

    collapseAllHelper(o, CollElement, CollInfo, disp);
}

function expandCollapseAll(o){
	/* IE has no method for getting elements by class name. Use querySelectorAll instead 
       Note: requires IE not to be in IE7 compatability mode */
    var SDCollapse = document.querySelectorAll(".SystemdefinedCollapse");
    var Button = null;

    if (GlobalCollapseState == "off"){
        var disp = "none";
        GlobalCollapseState = "on";

        if (o && o.firstChild.nodeType == 3) {
            var textNode = o.firstChild;
        	textNode.nodeValue = " 显示检查详细信息";
		}
    }else{
        var disp = "";
        GlobalCollapseState = "off";

        if (o && o.firstChild.nodeType == 3) {
            var textNode = o.firstChild;
        	textNode.nodeValue = " 隐藏检查详细信息";
		}
    }

    for (var i=0;i<SDCollapse.length;i++){
        Button = SDCollapse[i].nextSibling;
        while(Button.nodeType !== 1){
            Button = Button.nextSibling;
        }
        //Button = SDCollapse[i].parentNode.querySelector("span.SDCollapseControl");
        collapseSDHelper(Button, SDCollapse[i], disp, "all");
    }
		
    var AllCollapse = document.querySelectorAll(".AllCollapse");

    for (i=0;i<AllCollapse.length;i++){
        // get children of top level collapsible div
        var children = AllCollapse[i].parentNode.children;
        
        // collapsible button is first child
		Button = children[0];
        
        // get content to display in collapsed state
        // this is optional and last child of collapsible div 
        if (Button)
        {
            if (children.length>2)
            {
                collapseAllHelper(Button, AllCollapse[i], children[2], disp);
            }
            else
            {
                collapseAllHelper(Button, AllCollapse[i], null, disp);
            }
        }
    }
}


function expandCollapseAllOnLoad(){
    GlobalCollapseState = "on";
    var Switch = document.getElementById("ExpandCollapseAll");
    expandCollapseAll(Switch);
}
//END COLLAPSIBLE

 --></script></head>  
<body onload="updateHyperlink(); expandCollapseAllOnLoad();">  
<span id="top">

</span>
<!-- mdladv_ignore_start --><div id="Container"><!-- mdladv_ignore_finish -->
<!-- mdladv_ignore_start --><div class="ReportContent" id="pipelined_TOP"><!-- mdladv_ignore_finish --><table class="AdvTableNoBorder" width="100%" border="0">
<tr>
<td colspan="2" align="center">
<b>
代码生成顾问报告 - <font color="#800000">pipelined_TOP.slx</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
Simulink 版本: <font color="#800000">23.2</font>
</b>

</td>
<td align="right" valign="top">
<b>
模型版本: <font color="#800000">1.23</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
系统: <font color="#800000">pipelined_TOP</font>
</b>

</td>
<td align="right" valign="top">
<b>
当前运行: <font color="#800000">2025/06/26 16:52:21</font>
</b>

</td>

</tr>
<tr>
<td align="left" valign="top">
<b>
视为引用模型: <font color="#800000">off</font>
</b>

</td>
<td align="right" valign="top">
&#160;
</td>

</tr>

</table>
<br /><font color="#800000"><b>运行摘要</b></font><br /><table class="AdvTableNoBorder" width="60%" border="0">
<tr>
<th align="left" valign="top">
<b>
未完成
</b>

</th>
<th align="left" valign="top">
<b>
失败
</b>

</th>
<th align="left" valign="top">
<b>
警告
</b>

</th>
<th align="left" valign="top">
<b>
已申述
</b>

</th>
<th align="left" valign="top">
<b>
通过
</b>

</th>
<th align="left" valign="top">
<b>
信息
</b>

</th>
<th align="left" valign="top">
<b>
未运行
</b>

</th>
<th align="left" valign="top">
<b>
合计
</b>

</th>

</tr>
<tr>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEQSURBVHgBrVI7csIwEH3rMK7dZdI5J0jKZNLQJJM+aRPICRJuEG7gI+Ch5QD8ChoGOnwEOobONRgvWo0QDB9jD7xGK+3u05P2AReC9g+4XPbgLn5VWAXDN2WRykS4SevUHk9PEvDbSwUpB+rUw3HEcLhOnVFwQMCvz+pWCpBLN1WpOwwtAb8/+UicScbNh0qW7j0NBrGjtwn9F2gWeCgt/iRwjKYHFAWhIkvJbB712mwBt3fZjfMZ8PUpkb+jwIAJZ7FXYxTwVCV8fH+gAKKtgpRCFMcOQeIGynVx7lapVa60BDJPUFrLTQC2lrafSL1xA8y1TCWSW+GH+kesbGu0K5WxxBub8UJ/cihP1WqviTVjXFeRN9ClYgAAAABJRU5ErkJggg=="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAEDSURBVHgBrVIxCsJAENwVGzux1MZCxDKg+AYVLHxBYmXjI/IECxsr4wsshPgHEUkZxCJNLCWdnWf2SI41uYBBpzhud2fm9vYO4EcgD9x+18QXruJtvYAfCCHsiXfd5QwSsQNfIDaxUpMKF7emMxhdfOgsljkR5ahGHER0XKNrKoNYbGfJ3CQbSw3Kq0I1idu0hIc91JotrQHhtllLToI6N1Agku5Uyqc1jgpoQMTH+aRi2uvEhQZ0cmMwVDHtdYPVGvDWedu6QRI+ZkBPpBNzg+c95INUHUTcKCsuGGBAi/yJR6NnAYotlIHA+djzHfWVS5kkYmBXAJmIC2lrBYi4+C94A/a+eag5PUw2AAAAAElFTkSuQmCC"  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC"  /> 1
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADqSURBVHgBpZKvC8JAFMffZAPFslXTLBYNitVimBYx+A/of+CibYs2rcZVg4gYBYtFUJdWLFsxb0UUDOod3Insx43dBw7e4/i++753T3h/AQ5ywIlIAuvsgb65QPB8JQpUpQimVoNhU8W5QFooT7fg+XdIg1yQwDcHOKYtsMToRb1VwXHw+LlMNQMkRrbXzi10F1kAvWSPOyDnJSpuL/aRLsWoAvPDFeolBWy9i/M4cawDxGh5BOvkJopjHRDMnQMs/hy4kx4+afNQgSzQRVKMFXMLCWgbiRPqYNZv4AsW6GsNrRp2kBXuGXwAIu1X3q8Yjd0AAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAHTSURBVHgBrVO9ThtBEP7mLomDFEWXpEhFbD9BlC5SUGynSkdSpoiOMt2lovXdG0CqpEpIR2cXtPxJSNAgIyoaOKBBAuEzDRYSHmb29hYbRAWjk3a1O9/PzM4B9wy6dRIHAeBFBJpioJInUIfBHeAyQZyldxL4yatwwJgRYGAvGETEPGBLlDFxgubprMM4dPIyAtNv2T0dYid2e9L9mOw+o1FKsdzf0nPP2q4IOFZFHnLEziJRJXiDuDaNF6XnUo0/k5daEMBXcMCaidFQEgUvhi0069P4WJ3QlAADL3IEYu/tKIa5IFLwUtg2a+doG6vpWu7KRzjkgA3BXrSJ3WhT7JY1h6sG3BLwOLYE/GluEt1+TxSkUqaqI2BbatY/s4ot1MofaNEqK7gxN8lduS8aWti1JSDV9ev8d6TZQU4y1XbKjf9fRPmMhvsCMxfOAf0TVuxnh6pkSDSsMrLzHuNWUMeKw06fv2e6axo3jnp5Au2dBVPWtarzkAGDdzqV16+WBCGx/7d4SjYvQeZjvmGAvJ9oHs/mj1GETlZtrCeA9zDTODoSlJuQe/xA8+RPce6PMK+cr6NempfWBJr/7LH/+skjjy4uORWKX/IzfUPc3cBDxhXE6LNA6WFItQAAAABJRU5ErkJggg=="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsSAAALEgHS3X78AAACVElEQVQ4jV1TTWtTQRQ99868NKWUihRauypBkLppNy20CFl2adE/8HYuxIXEnX+hi/oLDOhWydLiyoVZtC6ahVCskUohGMWPSGyT997MlTvvJTZeGJiPe849cz9IRHDZKrvNmJhjEFchHhABGQtxWQvAXru2Wb/sPyao7DbXiKhObFaJDfT+7s2rADFevP8GCCA+U1clitu1zaMxQWW3uUzERyCaYxtBCTaWZvD8zvVAfn//DK9PfqgK+CwFID0RWfv0aOuUAwtRg4wZg1XyQeccjQ89HH8f4rCbgqMpkC3BTJUVMQegoVjzbHY7Jjb3wAZsbADrWpkvw7NFu5fBiaCbGJhoKihSJRC/+KR59tmKSExEITKIQtIAwuNbS1i/Nh0Ah1+GiPe/hne2paDGi1ei2BJRNXzDGDBbgBlsbU42TjWBiMMbYGFK5VzJoF8tcsAgMrkvc1ATv+riXXeYK+gmIBuFL6oC3QfVetYyidZb66QRiIuoHL5SJHmiV8JZgwaCAKZCMo0gkyAqzoXP6J1YCbQxqICOyQolNBE2B45UEsEN+i0l2FP1Pksg3uUgcUU1CtO9D62Y+4iDSy5UwR5rb4tLW9phWl+XDEI6nm7P48aVKBDcrpTD8mmCtP8LLhlq4lsfH67XbSiHyA7EH/ksmWNmuHSI9YW8adSWZgwWS1mYj+z8N8T7HiA7/w/TMhvbIBOtclTCg40FrMxPYzYidP44vDz+ibcnHYiXFhF22rXN0wmCkek4s41i79IqmxLEpTDlGWQX/TdEqLdrW//GGcBfdA38qIpY8foAAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
&#160;&#160;<img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAADhSURBVHgBtZI/CoMwFIdfQg/QyU3o4ubopjdw8gS9heANdOsVegGv0A66F3RzqOCkU92ypUYSCM0flOIHIY/k5cdHEoA/QaLI8/y9TBdLb48xTtI0fZkCqOd5wMYv8zxDXdes/FBKkyzLnmIPy43TNMEwDNrBOSOEHkVRXMXCSQ5wHAd83wcdYRiuc9M00LbtbSnvSgAz4KpGxnFcTYwGQRBYA7gBaAN2GOgDXNeFKIqsAVVVmQ3YhnTjWgghZgP2AocbsE9lNYjjWHuwLEvouk5ZVwxkvS3IX7mH7ezptfMFbNhwT85bFwwAAAAASUVORK5CYII="  /> 0
</td>
<td align="left" valign="top">
 1
</td>

</tr>

</table>
<!-- Compile Status Flag --><!-- Service Status Flag -->
<!-- mdladv_ignore_start --><div name = "Warning Check" id = "Warning Check" class="WarningCheck" style="margin-left: 0pt;"> <!-- mdladv_ignore_finish -->
<p><hr /></p>  <a name="CheckRecord_36"></a><div class="CheckHeader" id="Header_mathworks.codegen.CodeGenSanity">
<!-- mdladv_ignore_start --><img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAD3SURBVHgBzZI7EsFQFIb/I4oQRTSUsgOlNlqPGTtgB+yAHRg9Ywka/c0WFGp0aCgYxuvIZcwk8phkRuGr7uPc///P3AP8NVY9vbBqmgirSQRdiIrWApNhL01RTXUQB9FQDel+Wi/5tFmynWInTF2PnIDuSlu6q/kC1FxBHunIXDqRBKQ7GJ5iArX9UngTXJUe/NFJu/ZDBaQ7EZoIgtAStYwZKEA3ZeLcnzcrnLerLw3uuvcfd/vbKIExIsCgcnl6sFwJSHErS0rDOUqjuUeAiMeuFhxDEw2G8RmuVwtyaGIJvNmb02M2+RJ80MDuvxjnNT8wwy94AjZHSy+aGaDFAAAAAElFTkSuQmCC"  />&#160;<!-- mdladv_ignore_finish --><span class="CheckHeading" id="Heading_mathworks.codegen.CodeGenSanity">
对照代码生成目标检查模型配置设置</span>
<!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --><!-- mdladv_ignore_finish --></div>
<!-- mdladv_ignore_start --><div class="subsection"><!-- mdladv_ignore_finish --><p /><p /><p>
<br /><b>当前目标:</b> <font color="#0000FF">调试</font><br>代码生成目标不同于模型中设置的目标(未指定)。点击 '修改参数' 按钮以将当前目标存储在模型中。<br><br>以下参数值不是针对所选目标优化的。<br><br>要自动修复警告，请点击 '修改参数' 按钮，然后重新运行检查。要手动修复警告，请点击参数超链接打开 "配置参数" 对话框，并手动应用推荐值。<table class="AdvTable" border="1">
<tr>
<th align="left" valign="top">
<b>
参数
</b>

</th>
<th align="left" valign="top">
<b>
当前值
</b>

</th>
<th align="left" valign="top">
<b>
推荐值
</b>

</th>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP OptimizeBlockIOStorage"> 信号存储重用</a>
</td>
<td align="left" valign="top">
on
</td>
<td align="left" valign="top">
off
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP BuildConfiguration"> 编译配置</a>
</td>
<td align="left" valign="top">
Faster Builds
</td>
<td align="left" valign="top">
Debug
</td>

</tr>
<tr>
<td align="left" valign="top">
<a href="matlab: modeladvisorprivate openCSAndHighlight pipelined_TOP ShowEliminatedStatement"> 显示已消除模块</a>
</td>
<td align="left" valign="top">
off
</td>
<td align="left" valign="top">
on
</td>

</tr>

</table>
<br /><br />_________________________________________________________________________________________
</p>
<!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start --></div><!-- mdladv_ignore_finish -->
<span name = "EmbedImages" id="EmbedImages"></span><!-- mdladv_ignore_start -->
</div><!-- mdladv_ignore_finish --><!-- mdladv_ignore_start -->
</div><!-- mdladv_ignore_finish -->
</body>  
</html>  