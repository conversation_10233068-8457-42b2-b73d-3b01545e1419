function [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
    pipeline_adc_top(params, varargin)
%PIPELINE_ADC_TOP 流水线ADC系统顶层入口文件
%   v6.0 - 回归实际电路设计思路的重构版本
%   专注于理想流水线ADC的零误差输出正确性验证
%   验证理想流水线ADC能否像简单理想ADC一样输出理想的拟合波形
%
%   v6重构说明:
%   本版本完全回归实际电路设计思路，重点改进：
%   1. 移除复杂的有效性标记和握手信号机制
%   2. 简化时钟控制，仅使用非交叠时钟
%   3. 重新设计事件驱动状态机
%   4. 固定时间步长信号生成
%   5. 数据矩阵时延对齐和RSD校正
%
% === 项目结构和文件说明 ===
%
% 【核心模型文件】
%   pipeline_adc_top.m              - 系统顶层入口（本文件）
%   event_driven_pipeline_adc_v3.m  - 事件驱动流水线ADC主模型（调用v6核心）
%   event_processing_core_v6.m      - v6事件处理核心逻辑（重构版）
%   digital_correction_v4.m         - v4数字校正算法（时延对齐+RSD）
%   generate_timebase.m             - 固定时间步长信号生成（替代adaptive）
%   run_ideal_adc_v2.m              - 理想ADC对比基准
%
% 【测试验证文件】  
%   main_pipeline_test.m            - 主要功能验证测试
%   test_pipeline_adc_v2.m          - 完整技术规范验证
%   simple_test_pipeline.m          - 基本功能测试
%
% 【性能分析文件】
%   analyze_performance.m           - 性能分析和对比
%   calculate_sndr.m               - SNDR等性能指标计算
%   extract_dynamic_test_samples.m - 动态特性测试样本提取
%   visualize_results_v2.m         - 结果可视化
%
% 【晶体管仿真相关】（保留）
%   import_simulation_data.m       - 仿真数据导入
%   optimize_error_model.m         - 误差模型优化（用于对比）
%   各种HSPICE相关文件（.sp、.lib等）
%
% 输入参数:
%   params - ADC参数结构体，必须包含:
%       .fs         - 采样频率 (Hz)，建议100e6
%       .f_in       - 输入频率 (Hz)，建议23/1024*100e6（相干采样）
%       .Vref       - 参考电压 (V)，建议1.2V
%       .Vcm        - 共模电压 (V)，建议0.6V
%       .num_stages - 流水线级数，标准8级
%       .num_cycles - 仿真周期数，建议30
%
%   varargin - 可选参数:
%       'TestMode', 'ideal' - 测试模式 ('ideal'|'verification'|'comparison')
%       'DebugLevel', 1 - 调试级别 (0-3)
%       'PlotResults', true - 是否绘制结果
%       'SaveResults', false - 是否保存结果
%
% 输出参数:
%   analog_outputs - 各级模拟输出结构体（模拟输出1：SHA+8级流水线）
%   final_dac_output - 最终DAC转换的模拟信号输出（模拟输出2）
%   stage_digital_outputs - 各级数字输出结构体  
%   performance_metrics - 性能指标结构体（与理想ADC对比）
%
% === 零误差验证方法和预期结果 ===
%
% 验证目标:
%   1. 理想流水线ADC在零误差条件下应输出完美的正弦波形
%   2. 输出波形与run_ideal_adc.m的理想ADC高度一致（相关性>0.99）
%   3. SNDR应接近理论值（约60dB for 10bit ADC）
%   4. 各级数字输出应符合1.5bit量化规律
%
% 验证步骤:
%   1. 生成符合相干采样要求的输入信号（23/1024*100MHz，30周期）
%   2. 使用零误差参数运行事件驱动流水线ADC
%   3. 运行理想ADC作为对比基准
%   4. 计算相关性、SNDR、RMS误差等指标
%   5. 验证结果是否符合理想预期
%
% 预期结果:
%   - 相关系数 > 0.99（高度一致）
%   - SNDR差异 < 3dB（接近理想）
%   - RMS误差 < 1 LSB（量化噪声水平）
%   - 有效输出比例 > 95%（系统稳定）
%
% 使用示例:
%   % 标准理想验证
%   params = struct('fs', 100e6, 'f_in', 23/1024*100e6, 'Vref', 1.2, ...
%                   'Vcm', 0.6, 'num_stages', 8, 'num_cycles', 30);
%   [analog_out, dac_out, digital_out, metrics] = pipeline_adc_top(params);
%
%   % 详细验证模式
%   [analog_out, dac_out, digital_out, metrics] = pipeline_adc_top(params, ...
%       'TestMode', 'verification', 'DebugLevel', 2, 'PlotResults', true);
%
% 版本: v1.0 - 专注理想ADC验证的重构版本
% 作者: 流水线ADC设计团队
% 日期: 2024

    % 参数解析
    p = inputParser;
    addRequired(p, 'params', @isstruct);
    addParameter(p, 'TestMode', 'ideal', @(x) ismember(x, {'ideal', 'verification', 'comparison'}));
    addParameter(p, 'DebugLevel', 1, @(x) ismember(x, 0:3));
    addParameter(p, 'PlotResults', true, @islogical);
    addParameter(p, 'SaveResults', false, @islogical);
    parse(p, params, varargin{:});
    
    test_mode = p.Results.TestMode;
    debug_level = p.Results.DebugLevel;
    plot_results = p.Results.PlotResults;
    save_results = p.Results.SaveResults;

    % 验证和补全参数
    params = validate_and_complete_params(params);
    
    % 显示系统信息
    display_system_info(params, test_mode, debug_level);
    
    % 根据测试模式执行相应的验证流程
    switch test_mode
        case 'ideal'
            % 理想模式：专注零误差验证
            [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
                run_ideal_verification(params, debug_level);
                
        case 'verification'
            % 验证模式：完整的技术规范验证
            [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
                run_comprehensive_verification(params, debug_level);
                
        case 'comparison'
            % 对比模式：与晶体管仿真数据对比
            [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
                run_comparison_analysis(params, debug_level);
                
        otherwise
            error('不支持的测试模式：%s', test_mode);
    end
    
    % 验证结果评估
    verify_ideal_performance(performance_metrics, test_mode);
    
    % 可视化结果
    if plot_results
        visualize_results_v2(params, analog_outputs, final_dac_output, ...
                           stage_digital_outputs, performance_metrics, test_mode);
    end
    
    % 保存结果
    if save_results
        save_verification_results(params, analog_outputs, final_dac_output, ...
                                stage_digital_outputs, performance_metrics, test_mode);
    end
    
    % 输出验证总结
    display_verification_summary(performance_metrics, test_mode);
end

function params = validate_and_complete_params(params)
%VALIDATE_AND_COMPLETE_PARAMS 验证并补全参数结构体

    % 必需参数检查
    required_fields = {'fs', 'f_in', 'Vref'};
    for i = 1:length(required_fields)
        if ~isfield(params, required_fields{i})
            error('缺少必需参数: %s', required_fields{i});
        end
    end
    
    % 默认参数设置
    if ~isfield(params, 'Vcm')
        params.Vcm = params.Vref / 2;
    end
    
    if ~isfield(params, 'num_stages')
        params.num_stages = 8;
    end
    
    if ~isfield(params, 'num_cycles')
        params.num_cycles = 30;  % 符合相干采样要求
    end
    
    if ~isfield(params, 'resolution')
        params.resolution = 10;
    end
    
    if ~isfield(params, 'bits_per_stage')
        params.bits_per_stage = 1.5;
    end
    
    if ~isfield(params, 'flash_bits')
        params.flash_bits = 2;
    end
    
    % 验证相干采样频率要求
    expected_freq = 23/1024*100e6;
    if abs(params.f_in - expected_freq) > 1e3
        warning('输入频率 %.3f MHz 不符合相干采样要求 %.3f MHz', ...
                params.f_in/1e6, expected_freq/1e6);
        fprintf('建议使用: params.f_in = 23/1024*100e6; %% %.6f MHz\n', expected_freq/1e6);
    end
    
    % 参数合理性检查
    if params.f_in >= params.fs / 2
        warning('输入频率接近或超过奈奎斯特频率，可能导致混叠');
    end
    
    if params.num_stages ~= 8
        warning('标准流水线级数为8级，当前设置为%d级', params.num_stages);
    end
end

function display_system_info(params, test_mode, debug_level)
%DISPLAY_SYSTEM_INFO 显示系统信息

    fprintf('\n');
    fprintf('=================================================================\n');
    fprintf('流水线ADC系统 - 理想输出正确性验证平台\n');
    fprintf('=================================================================\n');
    fprintf('测试模式: %s\n', upper(test_mode));
    fprintf('调试级别: %d\n', debug_level);
    fprintf('\n');
    fprintf('系统参数:\n');
    fprintf('  采样频率: %.1f MHz\n', params.fs/1e6);
    fprintf('  输入频率: %.6f MHz (相干采样)\n', params.f_in/1e6);
    fprintf('  参考电压: %.2f V\n', params.Vref);
    fprintf('  共模电压: %.2f V\n', params.Vcm);
    fprintf('  流水线级数: %d级\n', params.num_stages);
    fprintf('  仿真周期: %d个周期\n', params.num_cycles);
    fprintf('\n');
    fprintf('验证目标:\n');
    fprintf('  1. 零误差条件下的理想输出验证\n');
    fprintf('  2. 与简单理想ADC的一致性检验\n');
    fprintf('  3. 18bit→10bit数字校正正确性验证\n');
    fprintf('  4. 事件驱动时序控制精度验证\n');
    fprintf('=================================================================\n');
end

function [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
    run_ideal_verification(params, debug_level)
%RUN_IDEAL_VERIFICATION 运行理想验证流程

    fprintf('\n=== 理想验证模式：零误差输出正确性验证 ===\n');
    
    % 步骤1: 生成高精度时间基准和信号
    fprintf('\n[步骤1/5] 生成高精度自适应时间基准...\n');
    [t_sampled, sine_input, clks_input, clkh_input] = generate_adaptive_timebase(params);
    
    % 生成差分输入信号（80%满量程，避免饱和）
    input_amplitude = 0.8 * params.Vref;
    Vin_p = params.Vcm + input_amplitude * sine_input;
    Vin_n = params.Vcm - input_amplitude * sine_input;
    
    num_samples = length(t_sampled);
    fprintf('  生成样本数: %d\n', num_samples);
    fprintf('  输入振幅: %.3f V (%.1f%% 满量程)\n', input_amplitude, 80);
    
    % 步骤2: 初始化输出结构
    fprintf('\n[步骤2/5] 初始化输出结构...\n');
    [analog_outputs, stage_digital_outputs, final_dac_output] = ...
        initialize_output_structures(num_samples, params.num_stages);
    
    % 步骤3: 运行理想流水线ADC（零误差）
    fprintf('\n[步骤3/5] 运行理想流水线ADC（零误差模型）...\n');
    
    % 设置零误差参数
    rel_error_params = zeros(params.num_stages, 2);
    fprintf('  误差参数: 全零矩阵 (%dx%d)\n', size(rel_error_params));
    
    % 调用事件驱动流水线ADC (更新为v3版本集成v5核心)
    try
        % 生成v3版本所需的边沿有效信号格式
        if ~isstruct(clks_input) || ~isfield(clks_input, 'rising')
            % 如果输入是传统数字时钟格式，需要转换为边沿有效信号
            [~, ~, clks_edges, clkh_edges] = generate_adaptive_timebase_v2(params);
        else
            clks_edges = clks_input;
            clkh_edges = clkh_input;
        end
        
        [final_dac_output, binary_output, stage_history, timing_info] = ...
            event_driven_pipeline_adc_v3(Vin_p, Vin_n, params, rel_error_params, ...
                                        t_sampled, clks_edges, clkh_edges);
        
        % 提取各级模拟输出
        analog_outputs = extract_analog_outputs(stage_history, params.num_stages);
        stage_digital_outputs = extract_digital_outputs(stage_history, params.num_stages);
        
        fprintf('  事件处理: %d个事件\n', timing_info.processed_events);
        fprintf('  有效输出: %d个样本 (%.1f%%)\n', timing_info.valid_outputs, ...
                100*timing_info.valid_outputs/num_samples);
                
    catch ME
        error('理想流水线ADC运行失败: %s', ME.message);
    end
    
    % 步骤4: 运行理想ADC对比基准 (v2版本)
    fprintf('\n[步骤4/5] 运行理想ADC对比基准 (v2版本)...\n');
    [ideal_dac_output, ~, ~, ~] = run_ideal_adc_v2(0, params, t_sampled, clkh);
    fprintf('  理想ADC输出范围: [%.4f, %.4f] V\n', min(ideal_dac_output), max(ideal_dac_output));
    
    % 步骤5: 计算性能指标和验证结果
    fprintf('\n[步骤5/5] 计算性能指标...\n');
    performance_metrics = calculate_ideal_verification_metrics(...
        final_dac_output, ideal_dac_output, analog_outputs, params, t_sampled);
    
    fprintf('=== 理想验证完成 ===\n');
end

function [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
    run_comprehensive_verification(params, debug_level)
%RUN_COMPREHENSIVE_VERIFICATION 运行完整验证流程

    fprintf('\n=== 完整验证模式：技术规范全面验证 ===\n');
    
    % 调用主验证脚本
    if exist('test_pipeline_adc_v2.m', 'file')
        run('test_pipeline_adc_v2.m');
    else
        % 如果v2版本不存在，使用现有版本
        warning('test_pipeline_adc_v2.m不存在，使用现有测试脚本');
        run('test_enhanced_pipeline_adc.m');
    end
    
    % 从工作空间获取结果
    if exist('final_dac_output', 'var')
        final_dac_output = evalin('base', 'final_dac_output');
        analog_outputs = evalin('base', 'analog_outputs');
        stage_digital_outputs = evalin('base', 'stage_digital_outputs');
        performance_metrics = evalin('base', 'performance_metrics');
    else
        error('完整验证模式执行失败，未获得有效结果');
    end
    
    fprintf('=== 完整验证完成 ===\n');
end

function [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
    run_comparison_analysis(params, debug_level)
%RUN_COMPARISON_ANALYSIS 运行对比分析

    fprintf('\n=== 对比分析模式：与晶体管仿真对比 ===\n');
    
    % 首先运行理想验证
    [analog_outputs, final_dac_output, stage_digital_outputs, performance_metrics] = ...
        run_ideal_verification(params, debug_level);
    
    % 如果存在晶体管仿真数据，进行对比
    if exist('import_simulation_data.m', 'file')
        fprintf('导入晶体管仿真数据进行对比...\n');
        try
            % 这里可以添加与晶体管仿真数据的对比逻辑
            % 暂时保留现有的理想验证结果
            performance_metrics.comparison_mode = true;
                 catch ME
             warning('PIPELINE_ADC:SimDataComparisonFailed', '晶体管仿真数据对比失败: %s', ME.message);
         end
    else
        warning('未找到晶体管仿真数据，仅进行理想验证');
    end
    
    fprintf('=== 对比分析完成 ===\n');
end

function verify_ideal_performance(performance_metrics, test_mode)
%VERIFY_IDEAL_PERFORMANCE 验证理想性能指标

    fprintf('\n=== 理想性能验证 ===\n');
    
    if isfield(performance_metrics, 'correlation')
        correlation = performance_metrics.correlation;
        if correlation > 0.99
            fprintf('? 相关性验证通过: %.6f (>0.99)\n', correlation);
        elseif correlation > 0.95
            fprintf('? 相关性基本通过: %.6f (>0.95)\n', correlation);
        else
            fprintf('? 相关性验证失败: %.6f (<0.95)\n', correlation);
        end
    end
    
    if isfield(performance_metrics, 'pipeline_sndr') && isfield(performance_metrics, 'ideal_sndr')
        sndr_diff = abs(performance_metrics.pipeline_sndr - performance_metrics.ideal_sndr);
        if sndr_diff < 3
            fprintf('? SNDR差异验证通过: %.2f dB (<3dB)\n', sndr_diff);
        elseif sndr_diff < 6
            fprintf('? SNDR差异基本通过: %.2f dB (<6dB)\n', sndr_diff);
        else
            fprintf('? SNDR差异验证失败: %.2f dB (>6dB)\n', sndr_diff);
        end
    end
    
    if isfield(performance_metrics, 'effectiveness')
        effectiveness = performance_metrics.effectiveness;
        if effectiveness > 95
            fprintf('? 系统稳定性验证通过: %.1f%% (>95%%)\n', effectiveness);
        elseif effectiveness > 90
            fprintf('? 系统稳定性基本通过: %.1f%% (>90%%)\n', effectiveness);
        else
            fprintf('? 系统稳定性验证失败: %.1f%% (<90%%)\n', effectiveness);
        end
    end
end

function display_verification_summary(performance_metrics, test_mode)
%DISPLAY_VERIFICATION_SUMMARY 显示验证总结

    fprintf('\n');
    fprintf('=================================================================\n');
    fprintf('验证总结 - %s模式\n', upper(test_mode));
    fprintf('=================================================================\n');
    
    if isfield(performance_metrics, 'pipeline_sndr')
        fprintf('流水线ADC SNDR: %.2f dB\n', performance_metrics.pipeline_sndr);
    end
    
    if isfield(performance_metrics, 'ideal_sndr')
        fprintf('理想ADC SNDR: %.2f dB\n', performance_metrics.ideal_sndr);
    end
    
    if isfield(performance_metrics, 'correlation')
        fprintf('输出相关性: %.6f\n', performance_metrics.correlation);
    end
    
    if isfield(performance_metrics, 'effectiveness')
        fprintf('系统有效性: %.1f%%\n', performance_metrics.effectiveness);
    end
    
    if isfield(performance_metrics, 'rms_error')
        fprintf('RMS误差: %.4f mV\n', performance_metrics.rms_error*1000);
    end
    
    fprintf('\n结论: ');
    if isfield(performance_metrics, 'correlation') && performance_metrics.correlation > 0.95
        fprintf('理想流水线ADC输出正确性验证通过！\n');
    else
        fprintf('需要进一步调试和优化。\n');
    end
    
    fprintf('=================================================================\n');
end

% 辅助函数
function [analog_outputs, stage_digital_outputs, final_dac_output] = ...
    initialize_output_structures(num_samples, num_stages)
    analog_outputs = struct();
    analog_outputs.sha = zeros(num_samples, 1);
    for stage = 1:num_stages
        analog_outputs.(['stage_', num2str(stage)]) = zeros(num_samples, 1);
    end
    stage_digital_outputs = struct();
    for stage = 1:num_stages
        stage_digital_outputs.(['stage_', num2str(stage)]) = zeros(num_samples, 2);
    end
    stage_digital_outputs.flash = zeros(num_samples, 2);
    final_dac_output = zeros(num_samples, 1);
end

function analog_outputs = extract_analog_outputs(stage_history, num_stages)
    analog_outputs = struct();
    if isfield(stage_history, 'sha')
        analog_outputs.sha = stage_history.sha;
    end
    for stage = 1:num_stages
        field_name = ['stage_', num2str(stage), '_analog'];
        if isfield(stage_history, field_name)
            analog_outputs.(['stage_', num2str(stage)]) = stage_history.(field_name);
        end
    end
end

function stage_digital_outputs = extract_digital_outputs(stage_history, num_stages)
    stage_digital_outputs = struct();
    for stage = 1:num_stages
        field_name = ['stage_', num2str(stage), '_digital'];
        if isfield(stage_history, field_name)
            stage_digital_outputs.(['stage_', num2str(stage)]) = stage_history.(field_name);
        end
    end
    if isfield(stage_history, 'flash')
        stage_digital_outputs.flash = stage_history.flash;
    end
end

function performance_metrics = calculate_ideal_verification_metrics(...
    pipeline_output, ideal_output, analog_outputs, params, t_sampled)
    
    performance_metrics = struct();
    
    % 选择有效数据
    valid_mask = abs(pipeline_output) > 0.001;
    valid_indices = find(valid_mask);
    
    if length(valid_indices) > 1024
        selected_indices = valid_indices(1:1024);
    else
        selected_indices = valid_indices;
    end
    
    if ~isempty(selected_indices)
        pipeline_data = pipeline_output(selected_indices);
        ideal_data = ideal_output(selected_indices);
        
        % 计算SNDR
        try
            [performance_metrics.pipeline_sndr, ~, ~, ~, ~] = ...
                calculate_sndr(pipeline_data, params.fs, params.f_in);
            [performance_metrics.ideal_sndr, ~, ~, ~, ~] = ...
                calculate_sndr(ideal_data, params.fs, params.f_in);
        catch
            performance_metrics.pipeline_sndr = -Inf;
            performance_metrics.ideal_sndr = -Inf;
        end
        
        % 计算相关性
        if length(pipeline_data) == length(ideal_data) && length(pipeline_data) > 1
            correlation_matrix = corrcoef(pipeline_data, ideal_data);
            performance_metrics.correlation = correlation_matrix(1, 2);
        else
            performance_metrics.correlation = 0;
        end
        
        % 计算RMS误差
        if length(pipeline_data) == length(ideal_data)
            performance_metrics.rms_error = sqrt(mean((pipeline_data - ideal_data).^2));
        else
            performance_metrics.rms_error = Inf;
        end
    else
        performance_metrics.pipeline_sndr = -Inf;
        performance_metrics.ideal_sndr = -Inf;
        performance_metrics.correlation = 0;
        performance_metrics.rms_error = Inf;
    end
    
    % 统计有效率
    performance_metrics.valid_samples = length(valid_indices);
    performance_metrics.total_samples = length(pipeline_output);
    performance_metrics.effectiveness = 100 * length(valid_indices) / length(pipeline_output);
end

function save_verification_results(params, analog_outputs, final_dac_output, ...
                                 stage_digital_outputs, performance_metrics, test_mode)
    save_file = sprintf('pipeline_adc_verification_results_%s.mat', test_mode);
    save(save_file, 'params', 'analog_outputs', 'final_dac_output', ...
         'stage_digital_outputs', 'performance_metrics', 'test_mode');
    fprintf('验证结果已保存至: %s\n', save_file);
end 