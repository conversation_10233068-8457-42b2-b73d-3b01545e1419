%
% Solve W from (Ids,Vgs,Vds,Vbs,L) with BSIM model equ.
%
function [vgs1,W1]=inv_bsim1(ids,gm,vds,vbs,L,PN)%vgs0
global Lb Ub gmid

if (PN>0)
   vgs0=-interp1(gmid(:,3), gmid(:,1),gm/abs(ids)); % gmidp
else
   vgs0=interp1(gmid(:,2), gmid(:,1),gm/ids); % gmidn
end
W0=inv_bsim(ids,vgs0,vds,vbs,L,PN);
Wdel=1000; vgsdel=10;k=0; 
e_ids=1; e_gm=1;
jacobi=zeros(2,2); alfa=1.0;
%while (Wdel>1e-2*W0||vgsdel>1e-2*abs(vgs0))
while (e_ids>1e-3||e_gm>1e-3)
    k=k+1;
    if (k==1)
       [ids0,gm0]=eval_bsim(vgs0,vds,vbs,W0,L,PN); 
       e_ids=abs(ids-ids0)/abs(ids);
       e_gm=abs(gm-gm0)/gm;
    end
    [ids1,gm1]=eval_bsim(1.01*vgs0,vds,vbs,W0,L,PN);
    jacobi(:,1)=[gm0; (gm1-gm0)/(0.01*vgs0)];
    [ids1,gm1]=eval_bsim(vgs0,vds,vbs,1.01*W0,L,PN);
    didw=(ids1-ids0)/(0.01*W0);
    dgmdw=(gm1-gm0)/(0.01*W0);
    jacobi(:,2)=[didw; dgmdw];
    del=inv(jacobi)*[ids-ids0; gm-gm0];
    alfa=min(alfa,1.0);
    vtmp=[vgs0; W0]+alfa*del; 
    [ids0,gm0]=eval_bsim(vtmp(1),vds,vbs,vtmp(2),L,PN);
    e_ids1=abs(ids-ids0)/abs(ids);
    e_gm1=abs(gm-gm0)/gm;
    while (e_ids1+e_gm1>e_ids+e_gm) 
        alfa=0.5*alfa;
        vtmp=[vgs0; W0]+alfa*del; 
        [ids0,gm0]=eval_bsim(vtmp(1),vds,vbs,vtmp(2),L,PN);
        e_ids1=abs(ids-ids0)/abs(ids);
        e_gm1=abs(gm-gm0)/gm;
    end
    alfa=1.1*alfa;
    e_ids=e_ids1; e_gm=e_gm1;
    vgs1=vtmp(1); W1=vtmp(2);
    %W1=W0+(ids-ids0)/didw;
    vgsdel=abs(vgs1-vgs0);
    Wdel=abs(W1-W0);
    W0=W1; vgs0=vgs1;
    if (k>20) break; 
    else continue
    end
end
end

