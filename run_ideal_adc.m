function [ideal_adc_output, Vin_p, Vin_n] = run_ideal_adc(phase, params, t_sampled)
% RUN_IDEAL_ADC 生成理想ADC输出波形
%   [ideal_adc_output, Vin_p, Vin_n] = RUN_IDEAL_ADC(phase, params, t_sampled)
%   根据给定相位生成差分输入信号，并进行理想量化
%
% 输入:
%   phase - 输入信号相位（弧度）
%   params - 参数结构体
%   t_sampled - 采样时间点
%
% 输出:
%   ideal_adc_output - 理想ADC输出数据（电压值格式）
%   Vin_p - 差分输入正端
%   Vin_n - 差分输入负端

try
    % 验证输入参数
    validateattributes(phase, {'numeric'}, {'scalar', 'real', 'finite'});
    validateattributes(t_sampled, {'numeric'}, {'vector', 'nonempty', 'finite'});
    
    % 生成差分输入信号
    Vin_p = params.Vcm + 0.5*params.Vref*sin(2*pi*params.f_in*t_sampled + phase);
    Vin_n = params.Vcm - 0.5*params.Vref*sin(2*pi*params.f_in*t_sampled + phase);
    
    % 计算单端输入
    Vin = Vin_p - Vin_n;
    
    % 预分配数组
    num_samples = length(t_sampled);
    digital_bits = zeros(num_samples, params.resolution);
    digital_code = zeros(num_samples, 1);
    
    for i = 1:num_samples
        % 将差分输入[-Vref,Vref]映射到[0,Vref]范围
        % 注意：Verilog-A中ADC输入假定为[0,Vref]范围
        unconverted = (Vin(i) + params.Vref) / 2;
        
        % 限制输入范围
        unconverted = max(0, min(unconverted, params.Vref));
        
        % 设置参考电压
        halfref = params.Vref / 2;
        
        % 从MSB到LSB逐位处理
        for bit = params.resolution:-1:1
            if unconverted > halfref
                digital_bits(i, params.resolution-bit+1) = 1;
                unconverted = unconverted - halfref;
            else
                digital_bits(i, params.resolution-bit+1) = 0;
            end
            unconverted = unconverted * 2;
        end
    end
    
    % 利用理想DAC相同的位权重计算数字编码
    for i = 1:num_samples
        out_scaled = 0;
        for bit = 1:params.resolution
            % 使用权重计算（512, 256, 128...）
            weight = 2^(params.resolution - bit);
            out_scaled = out_scaled + digital_bits(i, bit) * weight;
        end
        digital_code(i) = out_scaled;
    end
    
    scale_divider = 2^params.resolution; % 对10bit ADC为1024
    ideal_adc_output = params.Vref * digital_code / scale_divider;
    
    % 将输出从[0,Vref]范围转换回[-Vref,Vref]差分范围
    ideal_adc_output = 2 * ideal_adc_output - params.Vref;
    
catch ME
    error('理想ADC波形生成错误: %s', ME.message);
end
end 