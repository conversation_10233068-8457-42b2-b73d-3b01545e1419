function [L]=inv_bsimL(gds,vgs,vds,vbs,W,L0,PN)  %和inv_bsim0_2不同之处是把已知的W0当初值
global pathsp moslib Lb Ub gmid
Ldel=1000;k=0; L=L0;
itefail=0;
while (abs(Ldel)>1e-2*L)
    k=k+1;
    [ids0 gm0 gds0]=eval_bsim(vgs,vds,vbs,W,L0,PN);
    [ids1 gm1 gds1]=eval_bsim(vgs,vds,vbs,W,1.01*L0,PN);
    dgdsdL=(gds1-gds0)/(0.01*L0);
    L=L0+(gds-gds0)/dgdsdL; 
    Ldel=L-L0;
    L0=L;
    if (k>10|L<Lb) 
        itefail=1;
        disp('L<0 or k>10');
        L=Lb;
        break; 
    else
        continue
    end
end
