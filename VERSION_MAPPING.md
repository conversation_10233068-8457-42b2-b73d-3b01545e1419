# 流水线ADC项目版本映射文档

## 项目模块版本历史

### 核心算法模块

#### 信号生成模块
- `generate_adaptive_timebase.m` (v1) - 原始版本，传统0/1数字时钟信号
- `generate_adaptive_timebase_v2.m` (v2) - **新集成版本**，边沿触发信号生成
  - 特性：边沿有效信号格式 (.rising/.falling)
  - 优化：非交叠时钟边沿生成，精确时序控制
  - 接口：返回边沿结构体而非数字时钟

#### 事件处理核心模块
- `event_processing_core.m` (v1) - 原始基础版本
- `event_processing_core_v2.m` (v2) - 延迟缓冲优化版本
- `event_processing_core_v3.m` (v3) - 简化时序控制版本
- `event_processing_core_v4.m` (v4) - **新集成版本**，严格边沿触发逻辑
  - 特性：基于边沿有效信号的时钟控制
  - 架构：分离式流水线架构，独立级状态管理
  - 修复：V2版本索引计算错误
  - 集成：标签化数据管理系统

#### 流水线ADC主模型
- `event_driven_pipeline_adc.m` (v1) - 原始基础版本
- `event_driven_pipeline_adc_v2.m` (v2) - 零误差验证优化版本
- `event_driven_pipeline_adc_v3.m` (v3) - **新集成版本**，集成v4事件核心
  - 集成：event_processing_core_v4 + digital_correction_v3
  - 支持：边沿触发时钟信号格式
  - 架构：SHA + 8级1.5bit流水线 + 2bit Flash ADC
  - 校正：18bit→10bit标签化数字校正

#### 数字校正模块
- `digital_correction_v2.m` (v2) - 延迟同步优化版本
- `digital_correction_v3.m` (v3) - **新集成版本**，标签化数据对齐
  - 特性：FIFO缓冲队列系统
  - 对齐：基于标签的跨周期数据同步
  - 算法：增强的18bit→10bit全加器累加
  - 管理：自动历史数据管理机制

### 测试验证模块

#### 主测试脚本
- `main_pipeline_test.m` - **已更新**，集成新版本模块调用
  - 更新：调用generate_adaptive_timebase_v2
  - 更新：调用event_driven_pipeline_adc_v3
  - 更新：支持边沿信号格式验证
  - 增强：v3版本特性统计显示

#### 验证测试脚本
- `test_integrated_modules_validation.m` - **新增**，专用模块验证脚本
  - 验证：四个新模块的功能正确性
  - 测试：接口兼容性和集成稳定性
  - 报告：详细的验证结果统计

#### 完整测试脚本
- `test_pipeline_adc_v2.m` (v2) - 完整技术规范验证
- `test_pipeline_adc_v3.m` (v3) - V3版本功能测试
- `test_pipeline_adc_v3_complete.m` (v3完整版) - **新增**，完整的v3版本测试

### 支持工具模块

#### 性能分析
- `analyze_performance.m` - **保持不变**，兼容新版本接口
- `calculate_sndr.m` - **保持不变**，SNDR计算模块

#### 理想ADC对比
- `run_ideal_adc.m` (v1) - 原始版本
- `run_ideal_adc_v2.m` (v2) - **保持不变**，支持边沿信号输入

#### 可视化工具
- `visualize_results_v2.m` (v2) - **需要验证**，可能需要更新以支持新数据结构

## 版本兼容性矩阵

### 新版本集成组合 (推荐)
```
generate_adaptive_timebase_v2.m
    ↓ (边沿信号)
event_driven_pipeline_adc_v3.m
    ↓ (集成调用)
event_processing_core_v4.m + digital_correction_v3.m
    ↓ (输出)
main_pipeline_test.m (更新版本)
```

### 旧版本组合 (向后兼容)
```
generate_adaptive_timebase.m
    ↓ (数字时钟)
event_driven_pipeline_adc_v2.m
    ↓ (集成调用)
event_processing_core_v2.m + digital_correction_v2.m
    ↓ (输出)
main_pipeline_test.m (原始版本)
```

## 接口变更说明

### 时钟信号格式变更
**旧格式** (v1):
```matlab
[t, sine_wave, clks, clkh] = generate_adaptive_timebase(params);
% clks, clkh: 数字时钟信号 (0/1)
```

**新格式** (v2):
```matlab
[t, sine_wave, clks_edges, clkh_edges] = generate_adaptive_timebase_v2(params);
% clks_edges: {.rising, .falling} - 边沿有效信号 (logical)
% clkh_edges: {.rising, .falling} - 边沿有效信号 (logical)
```

### 流水线ADC调用变更
**旧调用** (v2):
```matlab
[adc_output, binary_output, stage_history, timing_info] = ...
    event_driven_pipeline_adc_v2(Vin_p, Vin_n, params, [], t_sampled, clks, clkh);
```

**新调用** (v3):
```matlab
[adc_output, binary_output, stage_history, timing_info] = ...
    event_driven_pipeline_adc_v3(Vin_p, Vin_n, params, [], t_sampled, clks_edges, clkh_edges);
```

## 迁移指南

### 从旧版本迁移到新版本
1. **更新时钟生成调用**：
   - 替换 `generate_adaptive_timebase` → `generate_adaptive_timebase_v2`
   - 更新变量名：`clks, clkh` → `clks_edges, clkh_edges`

2. **更新流水线ADC调用**：
   - 替换 `event_driven_pipeline_adc_v2` → `event_driven_pipeline_adc_v3`
   - 传递边沿信号结构体而非数字时钟

3. **验证测试结果**：
   - 运行 `test_integrated_modules_validation.m` 进行快速验证
   - 运行更新后的 `main_pipeline_test.m` 进行完整测试

### 保持向后兼容
- 所有旧版本模块文件都保留
- 可以随时回退到旧版本组合
- 新旧版本可以并存，用于对比验证

## 开发状态

### 已完成模块 ✓
- [x] generate_adaptive_timebase_v2.m - 边沿触发信号生成
- [x] event_processing_core_v4.m - 严格边沿触发事件核心
- [x] event_driven_pipeline_adc_v3.m - 集成v4核心的流水线ADC
- [x] digital_correction_v3.m - 标签化数字校正算法
- [x] main_pipeline_test.m - 主测试脚本更新
- [x] test_integrated_modules_validation.m - 验证脚本

### 需要验证模块 ⚠️
- [ ] visualize_results_v2.m - 可视化模块兼容性验证
- [ ] 完整系统测试 - 新版本组合的长时间运行验证

### 待开发模块 📋
- [ ] 误差注入模块更新 - 适配新的事件驱动架构
- [ ] 优化算法模块更新 - 支持标签化数据结构

## 技术改进总结

### 关键技术突破
1. **边沿触发时钟系统** - 解决传统数字时钟的时序问题
2. **标签化数据对齐** - 实现跨周期的精确数据同步
3. **分离式流水线架构** - 避免数据竞争，提高时序准确性
4. **FIFO缓冲队列** - 支持可变延迟的数据对齐处理

### 性能提升
- **时序精度**：从传统时钟的周期级精度提升到边沿级精度
- **数据对齐**：从简单延迟对齐提升到标签化精确对齐
- **处理效率**：通过事件驱动减少不必要的计算开销
- **扩展性**：模块化设计便于功能扩展和优化

## 版本映射与修复历史

### 🔧 **V5版本 - 数据传播问题修复版本 (2024年12月)**

#### 核心修复模块
- **`event_processing_core_v5.m`** - 修复数据传播逻辑重复和状态机时序问题
  - 消除CLKS上升沿处理中的重复数据传播逻辑
  - 简化状态检查条件，确保数据传播可靠执行
  - 优化时序控制，确保开关电容ADC正确工作
  - 修复数据传播计数器逻辑

#### 测试验证模块
- **`test_v5_fix.m`** - V5版本修复验证脚本
- **`debug_data_propagation.m`** - 数据传播问题诊断脚本

#### V5版本主要修复
1. **数据传播逻辑重复问题**：
   - 问题：在`process_clks_rising_v4`函数中存在两段几乎相同的数据传播代码
   - 修复：移除重复逻辑，确保每个边沿事件只执行一次数据传播
   
2. **状态检查条件过于复杂**：
   - 问题：同时检查`holding`和`latched_ready`状态导致条件难以满足
   - 修复：简化状态检查条件，明确各级的状态转换序列
   
3. **开关电容时序优化**：
   - 问题：SHA状态机时序逻辑不符合实际开关电容电路特性
   - 修复：严格按照开关电容时序要求实现状态转换

#### V5版本技术特性
- 状态机：`idle → latched_ready → holding → idle`
- 时序控制：非交叠时钟的正确处理
- 数据传播：单次执行，避免重复计数
- 错误处理：增强的调试和诊断功能

---

### 🔄 **V4版本 - SHA时序死锁修复版本**

#### 核心文件
- **`event_processing_core_v4.m`** - 事件驱动处理核心 (修复SHA状态机时序死锁)
  - 修复：添加`latched_ready`状态支持开关电容正确时序
  - 修复：CLKH下降沿正确设置SHA状态为`latched_ready`
  - 修复：CLKS上升沿正确检测`latched_ready`状态进行数据传播
  - 问题：存在数据传播逻辑重复，导致传播为0

#### V4版本已知问题（已在V5修复）
- 数据传播逻辑重复执行
- 状态检查条件过于复杂
- 数据传播计数器不准确

---

### 🔄 **V3版本 - 简化版本**

#### 核心文件
- **`event_processing_core_v3.m`** - 事件驱动处理核心 (简化时序控制)
- **`event_driven_pipeline_adc_v3.m`** - 集成事件驱动流水线ADC
- **`digital_correction_v3.m`** - 数字校正模块v3版本

#### V3版本特性
- 简化时序控制逻辑
- 移除复杂延迟缓冲
- 直接数据传递机制
- 优化数字校正算法

---

### 🔄 **V2版本 - 优化版本**

#### 核心文件
- **`event_processing_core_v2.m`** - 事件驱动处理核心 (优化延迟缓冲)
- **`digital_correction_v2.m`** - 数字校正模块v2版本
- **`generate_adaptive_timebase_v2.m`** - 自适应时间基准生成v2版本

#### V2版本特性
- 优化延迟缓冲机制
- 增强数字校正算法
- 边沿信号结构体格式(.rising/.falling)

---

### 📦 **V1版本 - 原始版本**

#### 核心文件
- **`event_processing_core_v1.m`** (重命名为 `event_processing_core.m`)
- **`generate_adaptive_timebase.m`** - 原始时间基准生成
- **`run_ideal_adc.m`** - 理想ADC对比

#### V1版本特性
- 原始版本基础功能
- 基础事件驱动处理
- 简单时序控制

---

## 🚀 **推荐使用版本**

### 当前最佳版本：**V5**
- ✅ 修复SHA时序死锁
- ✅ 修复数据传播逻辑重复
- ✅ 优化开关电容时序控制
- ✅ 简化状态机逻辑
- ✅ 增强调试和诊断功能

### 使用方法
```matlab
% 推荐使用V5版本进行流水线ADC仿真
[adc_output, binary_output, stage_history, timing_info] = ...
    event_processing_core_v5(Vin_p, Vin_n, t_sampled, clks_edges, clkh_edges, ...
                            timing_control, rel_error_params, params);

% 或者运行V5版本验证脚本
run('test_v5_fix.m');
```

---

## 🔧 **故障排除指南**

### 数据传播为0问题
- **症状**：`timing_info.data_propagations = 0`
- **诊断**：运行`debug_data_propagation.m`
- **解决方案**：使用V5版本修复

### 时序死锁问题  
- **症状**：SHA状态机卡在错误状态
- **解决方案**：使用V4或V5版本修复

### 有效输出为0问题
- **症状**：`timing_info.valid_outputs = 0`
- **解决方案**：
  1. 检查数据传播是否正常
  2. 检查数字校正模块是否正常
  3. 使用V5版本完整修复

---

## 📝 **版本升级路径**

```
V1 (原始) → V2 (优化) → V3 (简化) → V4 (修复时序死锁) → V5 (修复数据传播)
                                                                        ↑
                                                                  **推荐版本**
```

---

*最后更新：2024年12月*
*维护者：模拟IC设计专家*