function [ideal_adc_output, Vin_p, Vin_n, sampling_indices] = run_ideal_adc_v2(phase, params, t_sampled, clkh_signal)
% RUN_IDEAL_ADC_V2 生成理想ADC输出波形 (v2版本)
%   VERSION HISTORY:
%   v1: 原始版本 - 连续时间索引处理
%   v2: 添加基于CLKH时钟的采样点选择功能
%
%   v2版本主要改进:
%   1. 根据CLKH时钟信号选择合适的采样时刻
%   2. 在每个时钟周期选择稳定的采样点（75%位置）
%   3. 生成与实际ADC采样行为一致的理想输出
%   4. 返回采样索引用于后续分析
%
%   [ideal_adc_output, Vin_p, Vin_n, sampling_indices] = 
%       RUN_IDEAL_ADC_V2(phase, params, t_sampled, clkh_signal)
%
% 输入:
%   phase - 输入信号相位（弧度）
%   params - 参数结构体
%   t_sampled - 完整的时间向量
%   clkh_signal - CLKH时钟信号向量
%
% 输出:
%   ideal_adc_output - 理想ADC输出数据（电压值格式）
%   Vin_p - 差分输入正端
%   Vin_n - 差分输入负端
%   sampling_indices - 实际采样点的时间索引

try
    % 验证输入参数
    validateattributes(phase, {'numeric'}, {'scalar', 'real', 'finite'});
    validateattributes(t_sampled, {'numeric'}, {'vector', 'nonempty', 'finite'});
    validateattributes(clkh_signal, {'numeric'}, {'vector', 'nonempty', 'finite'});
    
    if length(t_sampled) ~= length(clkh_signal)
        error('时间向量和CLKH信号长度必须一致');
    end
    
    fprintf('理想ADC v2: 开始基于CLKH时钟的采样点选择...\n');
    
    % 步骤1: 检测CLKH时钟的高电平周期
    clkh_high_periods = detect_clkh_periods_v2(clkh_signal, t_sampled);
    
    % 步骤2: 在每个高电平周期选择稳定采样点（75%位置）
    sampling_indices = select_stable_sampling_points_v2(clkh_high_periods, 0.75);
    
    fprintf('理想ADC v2: 检测到 %d 个CLKH高电平周期，选择 %d 个采样点\n', ...
            length(clkh_high_periods), length(sampling_indices));
    
    % 步骤3: 生成完整的差分输入信号
    Vin_p = params.Vcm + 0.5*params.Vref*sin(2*pi*params.f_in*t_sampled + phase);
    Vin_n = params.Vcm - 0.5*params.Vref*sin(2*pi*params.f_in*t_sampled + phase);
    
    % 步骤4: 在采样点进行理想量化
    ideal_adc_output = perform_ideal_quantization_v2(Vin_p, Vin_n, sampling_indices, params);
    
    fprintf('理想ADC v2: 完成 %d 个有效采样点的量化处理\n', length(sampling_indices));
    
catch ME
    error('理想ADC v2波形生成错误: %s', ME.message);
end
end

function clkh_periods = detect_clkh_periods_v2(clkh_signal, t_sampled)
%DETECT_CLKH_PERIODS_V2 检测CLKH时钟的高电平周期 (v2版本)
    
    % 检测CLKH上升沿和下降沿
    clkh_diff = diff([0; clkh_signal(:)]);
    rising_edges = find(clkh_diff > 0.5);
    falling_edges = find(clkh_diff < -0.5);
    
    % 确保每个上升沿都有对应的下降沿
    clkh_periods = [];
    
    for i = 1:length(rising_edges)
        rise_idx = rising_edges(i);
        
        % 寻找对应的下降沿
        fall_candidates = falling_edges(falling_edges > rise_idx);
        if ~isempty(fall_candidates)
            fall_idx = fall_candidates(1);
            
            % 验证高电平周期的有效性
            period_length = fall_idx - rise_idx;
            if period_length > 10  % 最小周期长度要求
                clkh_periods(end+1, :) = [rise_idx, fall_idx];
            end
        end
    end
    
    fprintf('检测到 %d 个有效CLKH高电平周期\n', size(clkh_periods, 1));
end

function sampling_indices = select_stable_sampling_points_v2(clkh_periods, position_ratio)
%SELECT_STABLE_SAMPLING_POINTS_V2 选择稳定的采样点 (v2版本)
%   position_ratio: 在高电平周期内的位置比例（0.75表示75%位置）
    
    sampling_indices = [];
    
    for i = 1:size(clkh_periods, 1)
        rise_idx = clkh_periods(i, 1);
        fall_idx = clkh_periods(i, 2);
        
        % 计算稳定采样点位置（75%位置）
        period_length = fall_idx - rise_idx;
        stable_offset = round(period_length * position_ratio);
        stable_idx = rise_idx + stable_offset;
        
        % 确保索引在有效范围内
        if stable_idx <= fall_idx && stable_idx > 0
            sampling_indices(end+1) = stable_idx;
        end
    end
    
    sampling_indices = sort(sampling_indices);
    fprintf('在每个CLKH周期的 %.1f%% 位置选择采样点\n', position_ratio*100);
end

function ideal_output = perform_ideal_quantization_v2(Vin_p, Vin_n, sampling_indices, params)
%PERFORM_IDEAL_QUANTIZATION_V2 在采样点进行理想量化 (v2版本)
    
    % 初始化输出数组（与输入时间向量同长度）
    ideal_output = zeros(size(Vin_p));
    
    % 计算差分输入
    Vin = Vin_p - Vin_n;
    
    % 在每个采样点进行量化
    for i = 1:length(sampling_indices)
        idx = sampling_indices(i);
        
        if idx <= length(Vin)
            % 将差分输入[-Vref,Vref]映射到[0,Vref]范围
            unconverted = (Vin(idx) + params.Vref) / 2;
            
            % 限制输入范围
            unconverted = max(0, min(unconverted, params.Vref));
            
            % 逐位量化处理
            digital_bits = zeros(1, params.resolution);
            halfref = params.Vref / 2;
            
            for bit = params.resolution:-1:1
                if unconverted > halfref
                    digital_bits(params.resolution-bit+1) = 1;
                    unconverted = unconverted - halfref;
                else
                    digital_bits(params.resolution-bit+1) = 0;
                end
                unconverted = unconverted * 2;
            end
            
            % 计算数字编码
            digital_code = 0;
            for bit = 1:params.resolution
                weight = 2^(params.resolution - bit);
                digital_code = digital_code + digital_bits(bit) * weight;
            end
            
            % 转换为模拟输出电压
            scale_divider = 2^params.resolution;
            analog_output = params.Vref * digital_code / scale_divider;
            
            % 转换回[-Vref,Vref]差分范围
            ideal_output(idx) = 2 * analog_output - params.Vref;
        end
    end
    
    fprintf('完成 %d 个采样点的理想量化处理\n', length(sampling_indices));
end 