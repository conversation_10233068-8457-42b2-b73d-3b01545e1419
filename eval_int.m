function [pf2,ysp]=eval_int(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,<PERSON><PERSON><PERSON>,Cs1,Cs2,Ci1,Ci2,Co)
%global pathsp
%IB1=IB;
%IB2=IB;

pf2=0;ysp=zeros(100000,2);
global u0 G0 ovsht 
    fp=fopen('INT.sp','w+');
    fprintf(fp,'%s\n','*INTEGRATOR')
    fprintf(fp,'%s\n','.OPTIONs GMINDC=1E-9 post=2 measfall=0 ingold=2 NOMOD accurate');%numdgt=10 measdgt=10
    fprintf(fp,'%s\n','.subckt two_stage_opamp_CTCMFB AVDD AVSS Vcm Vin Vip Voutn Voutp ');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M0 net92 net021 AVSS AVSS NCH W=',W0,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M1 net90 Vip net92 net92 NCH W=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M2 net96 Vin net92 net92 NCH W=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M3 net90 vcmfb AVDD AVDD PCH W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M4 net96 vcmfb AVDD AVDD PCH W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M5 Voutp net90 AVDD AVDD PCH W=',W6,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M6 Voutn net96 AVDD AVDD PCH W=',W6,'U ' ,'L=',L6,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M7 Voutn net021 AVSS AVSS NCH W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M8 Voutp net021 AVSS AVSS NCH W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M9 net027 net021 AVSS AVSS NCH W=',W9,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M10 vcmfb net048 net027 AVSS NCH W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M11 net028 Vcm net027 AVSS NCH W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M12 vcmfb vcmfb AVDD AVDD PCH W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M13 net028 net028 AVDD AVDD PCH W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M14 net021 net021 AVSS AVSS NCH W=',W14,'U ' ,'L=',L,'U');
    fprintf(fp,'%s\n','R1 Voutp net048 100K');
    fprintf(fp,'%s\n','R2 Voutn net048 100K');
    fprintf(fp,'%s\n','C1 Voutn net048 260f');
    fprintf(fp,'%s\n','C2 Voutp net048 260f');
    fprintf(fp,'%s%9.5f%s\n','Cc1 net038 Voutn ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Cc2 Voutp net037 ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Rc1 net96 net038',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','Rc2 net037 net90',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','IBIAS AVDD net021  ',Ibias*1e6,'U');
    fprintf(fp,'%s\n','.ENDS');
    
    
    fprintf(fp,'%s\n','.subckt int1 AVDD AVSS Vcm  vincm dac+ dac- p1 p2  vin vip voutn voutp');
    fprintf(fp,'%s\n','X7 (AVDD AVSS Vcm net7 net8 voutn voutp) two_stage_opamp_CTCMFB');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms1 net3 p1 vip AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp1 net3 p2 vip AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms2 net6 p1 Vincm AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp2 net6 p2 Vincm AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms3 Vincm p1 net4 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp3 Vincm p2 net4 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms4 vin p1 net18 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp4 vin p2 net18 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms5 net8 p2 net6 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp5 net8 p1 net6 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms6 dac+ p2 net3 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp6 dac+ p1 net3 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms7 net18 p2 dac- AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp7 net18 p1 dac- AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Ms8 net4 p2 net7 AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Msp8 net4 p1 net7 AVDD PCH W=',Wsp,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s\n','Cs1 net3 net6 ',Cs1,'P');
    fprintf(fp,'%s%9.5f%s\n','Cs2 net18 net4 ',Cs2,'P');
    fprintf(fp,'%s%9.5f%s\n','Ci1 net8 voutn ',Ci1,'P');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Mreset1 net8 p1 voutn AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s%9.5f%s\n','Ci2 net7 voutp ',Ci2,'P');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','Mreset2 net7 p1 voutp AVSS NCH W=',Ws,'U ' ,'L=',Ls,'U');
    fprintf(fp,'%s\n','.ENDS');
    
   
   
   
    
    fprintf(fp,'%s\n','X6 AVDD AVSS Vcm vincm  dac+ dac- p1  p2  vin vip voutn voutp int1');
    fprintf(fp,'%s%9.5f\n','VDD (AVDD 0) DC  ', Vdd);
    fprintf(fp,'%s%9.5f\n','VSS (AVSS 0) DC ', Vss);
    fprintf(fp,'%s\n','V31 (Vcm 0) 0.9 ');
    fprintf(fp,'%s\n','V32 (Vincm 0) 0.9 ');
    %1n是不交叠时钟
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1  p1  0  dc 0 pulse(0 1.8V 0 1n 1n ', pw1, 'n ' ,Ts, 'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1-  p1-  0  dc 0 pulse(1.8 0 0 1n 1n ' , pw1, 'n ' ,Ts, 'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2  p2  0  dc 0 pulse(1.8V 0 0 1n 1n ', pw2,'n ' ,Ts,'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2-  p2-  0  dc 0 pulse(0 1.8v 0 1n 1n ', pw2,'n ' ,Ts,'n )');
    fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1  p1  0  dc 0 pulse(0 1.8V 10p 10p 10p ', pw1, 'n ' ,Ts, 'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk1-  p1-  0  dc 0 pulse(1.8 0 0 0n 0n ' , pw1, 'n ' ,Ts, 'n )');
    fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2  p2  0  dc 0 pulse(1.8V 0 10p 10p 10p ', pw2,'n ' ,Ts,'n )');
%     fprintf(fp,'%s%1.1f%s%1.1f%s\n','Vclk2-  p2-  0  dc 0 pulse(0 1.8v 0 0n 0n ', pw2,'n ' ,Ts,'n )');
    fprintf(fp,'%s\n','vdac+  dac+ 0  0.9');
    fprintf(fp,'%s\n','vdac-  dac- 0  0.9');
    fprintf(fp,'%s%9.5f%s\n','CL1 Voutn 0 ',Co,'P');
    fprintf(fp,'%s%9.5f%s\n','CL2 Voutp 0 ',Co,'P');
%     fprintf(fp,'%s\n','Vind  101  0  pulse(0 0.5 0 10p 10p 1u 2u)');
%     fprintf(fp,'%s\n','vcm 102 0 0.9 ' );
%     fprintf(fp,'%s\n','EIN+ vip 102  101  0  0.5');
%     fprintf(fp,'%s\n','EIN- vin 102  101  0  -0.5');
%     fprintf(fp,'%s%s%1.1f%s%4.5f%s\n','vvip  vip  0   sin(0.9',' ' ,amp, ' ' ,fin, ' 0 0 0)');
%     fprintf(fp,'%s%s%1.1f%s%4.5f%s\n','vvin  vin  0   sin(0.9',' ',amp, ' ' ,fin, ' 0 0 180)');
    fprintf(fp,'%s%1.3f%s%1.3f%s%1.3f%s\n','Vvip  vip  0   pulse(0.9 ', 0.9+u0/2 ,' 10p 10p 10p ', T1/2 , 'n ' ,T1, 'n )');
    fprintf(fp,'%s%1.3f%s%1.3f%s%1.3f%s\n','Vvin  vin  0   pulse(0.9 ', 0.9-u0/2 ,' 10p 10p 10p ', T1/2 , 'n ' ,T1, 'n )');
    fprintf(fp,'%s\n','.OP');
    fprintf(fp,'%s%4.4f%s\n','.TRAN 0.001n ',Tsim1,'u' );
    fprintf(fp,'%s\n','.MEAS TRAN VMAX MAX V(Voutn,Voutp) FROM=50n TO=100n');
    fprintf(fp,'%s\n','.MEAS TRAN VMIN MIN V(Voutn,Voutp) FROM=50n TO=100n');
    fprintf(fp,'%s\n','.MEAS TRAN TRISE TRIG V(Voutn,Voutp) VAL=''VMIN+0.45*(VMAX-VMIN)'' RISE=1' );
    fprintf(fp,'%s\n','+ TARG V(Voutn,Voutp) VAL=''VMIN+0.5*(VMAX-VMIN)'' RISE=1');
    fprintf(fp,'%s\n','.MEAS SR PARAM=''(VMAX-VMIN)*0.05/TRISE'' ');
    fprintf(fp,'%s\n','.print tran v(voutn,voutp)');
    fprintf(fp,'%s\n','.lib ''rf018.l'' TT');
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    system('C:\synopsys\Hspice_C-2009.09\BIN\hspice.exe  -i INT.sp -o INT -b');%jxm_hspice
       
    
    fp1=fopen('INT.mt0','r+');
	for m=1:4
        tline=fgets(fp1);
    end
    mt=fscanf(fp1,'%lf',4);
    fclose(fp1);
    SR=mt(4)*1.0E-6;   
    pf2=SR;
    fp1=fopen('INT.lis','r+'); 
    nt=0;
    while~feof(fp1)
        tline=fgets(fp1);
%         if(strncmp(tline,'            time',16)==1&&nt==0)
          if(strncmp(tline,'x',1)==1&&nt==0)
             for j=1:4 
                 tline=fgets(fp1);
                 stmp=fscanf(fp1,'%e%e',[1 2]);
             end
             for k=1
                 ysp(k,1:2) = [0 0 ];
             end
             for k =2:Tsim1*1000000 
                
                tmp=fscanf(fp1,'%e%e',[1 2]);
                ysp(k,1:2) = tmp;
                
            end

        end
    end
    fclose(fp1);
%     h=ysp(2,1)-ysp(1,1);
%     l=length(ysp(:,2));
%     a1=floor((l+0.5)/2);
%     ysp = ysp(1:a1 ,2);
%     b=floor((a1+0.5)/2);
%     ysp = ysp(b:end);
%     for k=1:size(ysp,1)
%         if (ysp(k)>u0*G0*(1-ovsht))
%            tssp=h*k;
%            break;
%         end
%     end
%     ovshtsp=0.0; tovsp=ysp(end,1);
%     for k1=k:size(ysp,1)
%         if (ysp(k1)<ysp(k1-1))
%             ovshtsp=ysp(k1-1)/(u0*G0)-1;
%             tovsp=ysp(k1-1,1);
%             break;
%         end
%     end
end