fclose all;
clear;
clc;

global pathsp moslib Lb Ub gmid 
global L Cc Vdd Vss CL Av ts ovsht  Vinmax Vinmin vsd6 vds7 VTn Cox icompen

% HSPICE位置及PDK
pathsp='D:\AnalogIC\HSPICE\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe';
moslib='.LIB ''ms018_v1p9.lib'' TT';

kBOLTSMAN=1.381e-23; Temp=273+25; gamman=1;
epislon=3.9*8.854e-12; 
un=275.5555875e-4; up=116.6094811e-4; 
toxn=4.1e-9; toxp=toxn;  %0.18um
UnCox=1e6*un*epislon/toxn; UpCox=1e6*up*epislon/toxp;
Cox=epislon/toxp;
VTn=0.47; VTp=-0.46; % 0.18um  

fp=fopen('gm_id.txt','r+');
if (fp<0)
    L=0.2; W=5; 
    k=0; 
    for vgsk=0.3:0.05:1.2
        k=k+1;
        vdsk=vgsk; vbs=0;
        [idsk,gmk,gdsk,cgsk]=eval_bsim(-vgsk,-vdsk,vbs,W,L,1);
        idp(k)=-idsk;
        gmidp(k)=-gmk/idsk; 
        gdsidp(k)=-gdsk/idsk; 
        cgsp(k)=-cgsk/idsk;
        
        [idsk,gmk,gdsk,cgsk]=eval_bsim(vgsk,vdsk,vbs,W,L,-1);
        idn(k)=idsk;
        gmidn(k)=gmk/idsk;
        gdsidn(k)=gdsk/idsk;
        cgsn(k)=cgsk/idsk;
    end  
    gmid=[(0.3:0.05:1.2)' gmidn' gmidp' idn' idp' gdsidn' gdsidp' cgsn' cgsp']; 
    % 'Vgs'gmidn'gmidp'idn'idp'gdsidn'gdsidp'cgsn'cgsp'
    save gm_id.txt gmid -ascii;
    figure; plot(gmid(:,1),[gmid(:,2) gmid(:,3)]);
else
    fclose(fp);
    load gm_id.txt -ascii;
    gmid=gm_id;
end
% Performance Specifications获取gmid数据

Vinmax=1.4; Vinmin=0.4;   %0.18um             %输入范围
Av    = 80; 
Sn    = 6.5e-9;   %Sn=1.8e-8;  % V/root Hz    %Sn:等效输入热噪声
ovsht = 1e-2;     %peak overshoot             %尖峰过冲电压
ts    = 2e-8;     %1 Settling time            %建立时间
UGB   = 600e6;                                %单位增益带宽600MHz
SR    = 200e6;                                %目标压摆率40V/us
PM    = 60;                                   %相位裕度60°

% 运放设计过程
% 单位换算
UGB = UGB*1e-6;
SR = SR*1e-6;

L  = 0.2;
CL = 0.4;   %所有电容不带单位

Vdd=1.8; Vss=0;  Lb=0.18; Ub=100;  %0.18um
Vout0=0.5*(Vdd+Vss);
Vin1=Vout0; Vin2=Vout0;
vbs = 0;
Vcmfb = 1.2;

Cc    = 0.1;        %米勒电容Cc＞0.22CL
Ibias = SR*Cc*1e-6; %尾管M0偏置电流I0

K     = tan(PM*pi/180); %根据相位裕度确定分离因子K:PM = atan(K)*180/pi;
Kn    = UnCox;          %工艺参数K
Kp    = UpCox;

% 先确定gm1,ids1
ids1  = 0.5*Ibias;
gm1   = UGB*Cc*1e-6;

% 为有60相位裕度，确定第二级极点为2.2UGB，由此确定gm6
vsd6  = Vdd-Vout0; vds7 = Vout0-Vss;
gm6   = 2.2*gm1*(CL/Cc);
Vdsat6= 0.1;
S6    = gm6/(Kp*Vdsat6);            % 先由gm6确定I6，单位暂时不确定
ids6  = (gm6^2)/(2*Kp*S6);
% ids6  = SR*(Cc+CL)*1e-6;
[vgs6,W6] = inv_bsim1(-ids6,gm6,-vsd6,0,L,1);

% 按照最大输入电压指标确定S3
vcmfb = 1.2;        %M3.M4共模反馈偏置
vsg3  = Vdd-vcmfb;
vsd3  = -vgs6;
vcmfb = Vdd-vsg3;
% S3    = Ibias/(Kn*(Vdd-Vinmax-VTp+VTn)^2);
% W3    = S3*L;
W3    = inv_bsim(-ids1,-vsg3,-vsd3,0,L,1);
[ids3,gm3,gds3,cgs3] = eval_bsim(-vsg3,-vsd3,0,W3,L,1); %求gm3
if(gm3/(2*cgs3)<=(10*UGB))
    W3 = W3+0.01;
end
% 设计S1所希望达到的GBW
vgs1  = (0.1+VTn);
Vs1   = Vin1-vgs1; 
vds1  = Vdd-vsd3-Vs1;
[vgs1, W1] = inv_bsim1(ids1,gm1,vds1,0,L,-1);
% 尾管
vds0  = Vs1-Vss;
Vov0  = Vinmin-Vss-vgs1; 
vgs0  = Vov0+VTn;
W0    = inv_bsim(Ibias,vgs0,vds0,0,L,-1);
% 第二级尾管
vgs7  = vgs0;
W7    = inv_bsim(ids6,vgs7,vds7,0,L,-1);

Rc=(1+CL/Cc)/gm6; 

% M14电流偏置
vgs14 = vgs0;
vds14 = vgs0;
W14   = inv_bsim(Ibias,vgs14,vds14,0,L,-1);
% 共模反馈
% 共模反馈
ids9  = 2*Ibias; %Ids9=2*Ibias
vgs9  = vgs0;
vds9  = 0.4; %原本的vds9=vds0偏小
W9    = inv_bsim(ids9,vgs9,vds9,vbs,L,-1);
vgs10 = Vout0-Vov0;
vds10 = vcmfb-vds9;
W10   = inv_bsim(ids9/2,vgs10,vds10,vbs,L,-1);
vsg12 = vsg3;
vsd12 = vsg12;
W12   = inv_bsim(-ids9/2,-vsg12,-vsd12,vbs,L,1);
%仿真
pf    = eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,Cc,Ibias,Vdd,Vss,CL,Rc);