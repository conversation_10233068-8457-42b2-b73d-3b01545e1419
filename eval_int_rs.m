function [y]=eval_int_rs(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,<PERSON><PERSON>,<PERSON><PERSON>s,Vdd,Vss,CL,Rc,Cs1,Ci1,Rf,R1,Rs)    
    global  pathsp moslib ovsht t u0 G0
    fp=fopen('INT_rs.sp','w+');
    fprintf(fp,'%s\n','*integrator');
    fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD RUNLVL=0 numdgt=10 measdgt=10 accurate');  %
    fprintf(fp,'%s\n','.subckt two_stage_opamp_CTCMFB AVDD AVSS Vcm Vin Vip Voutn Voutp ');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M0 net92 net021 AVSS AVSS NCH W=',W0,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M1 net90 Vip net92 net92 NCH W=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M2 net96 Vin net92 net92 NCH W=',W1,'U ' ,'L=',L1,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M3 net90 vcmfb AVDD AVDD PCH W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M4 net96 vcmfb AVDD AVDD PCH W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M5 Voutp net90 AVDD AVDD PCH W=',W6,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M6 Voutn net96 AVDD AVDD PCH W=',W6,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M7 Voutn net021 AVSS AVSS NCH W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M8 Voutp net021 AVSS AVSS NCH W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M9 net027 net021 AVSS AVSS NCH W=',W9,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M10 vcmfb net048 net027 AVSS NCH W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M11 net028 Vcm net027 AVSS NCH W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M12 vcmfb vcmfb AVDD AVDD PCH W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M13 net028 net028 AVDD AVDD PCH W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M14 net021 net021 AVSS AVSS NCH W=',W14,'U ' ,'L=',L,'U');
    fprintf(fp,'%s\n','R1 Voutp net048 100K');
    fprintf(fp,'%s\n','R2 Voutn net048 100K');
    fprintf(fp,'%s\n','C1 Voutn net048 260f');
    fprintf(fp,'%s\n','C2 Voutp net048 260f');
    fprintf(fp,'%s%9.5f%s\n','Cc1 net038 Voutn ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Cc2 Voutp net037 ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Rc1 net96 net038',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','Rc2 net037 net90',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','IBIAS AVDD net021  ',Ibias*1e6,'U');  
    fprintf(fp,'%s\n',moslib);
    fprintf(fp,'%s\n','.ENDS');

    
    fprintf(fp,'%s\n','X1 AVDD AVSS Vcm Vin Vip Voutn Voutp two_stage_opamp_CTCMFB'); 
    fprintf(fp,'%s%9.5f\n','VDD AVDD 0 DC  ', Vdd);
    fprintf(fp,'%s%9.5f\n','VSS AVSS 0 DC ', Vss);
    fprintf(fp,'%s%7.3f%s%7.3f%s\n','Vind  101  0 PWL(0 0 0.1n', u0, ' 0.2U', u0, ')');
%     fprintf(fp,'%s\n','Vind  101  0  pulse(0 1 0 10p 10p 100n 200n)');
    fprintf(fp,'%s\n','vincm 102 0 0.9 ' );
    fprintf(fp,'%s\n','vcm Vcm 0 0.9 ' );
    fprintf(fp,'%s\n','EIN+ 103 102  101  0  0.5');
    fprintf(fp,'%s\n','EIN- 104 102  101  0  -0.5');
    fprintf(fp,'%s%9.5f%s\n','Cs1 105 Vip ',Cs1,'P');
    fprintf(fp,'%s%9.5f%s\n','Cs2 106 Vin ',Cs1,'P');
    fprintf(fp,'%s%9.5f%s\n','Cf1 Vip Voutn ',Ci1,'P');
    fprintf(fp,'%s%9.5f%s\n','Cf2 Vin Voutp ',Ci1,'P');
    fprintf(fp,'%s%9.5f%s\n','Rs1 103 105 ',Rs,'k');
    fprintf(fp,'%s%9.5f%s\n','Rs2 104 106 ',Rs,'k');
    fprintf(fp,'%s%9.5f%s\n','R1 105 Vip ',R1,'G');
    fprintf(fp,'%s%9.5f%s\n','R2 106 Vin ',R1,'G');
    fprintf(fp,'%s%9.5f%s\n','Rf1 Vip Voutn ',Rf,'G');
    fprintf(fp,'%s%9.5f%s\n','Rf2 Vin Voutp ',Rf,'G');
    fprintf(fp,'%s%9.5f%s\n','CL1 Voutn 0 ',CL,'P');
    fprintf(fp,'%s%9.5f%s\n','CL2 Voutp 0 ',CL,'P');
    fprintf(fp,'%s\n','.OP');
    fprintf(fp,'%s\n','.option captab');
    fprintf(fp,'%s\n','.TRAN 0.001n 100n');
    fprintf(fp,'%s\n','.MEAS TRAN VMAX MAX V(Voutp,Voutn) FROM=0 TO=100n');
    fprintf(fp,'%s\n','.MEAS TRAN VMIN MIN V(Voutp,Voutn) FROM=0 TO=100n');
%     fprintf(fp,'%s\n','.MEAS TRAN VMAX MAX V(4,3) FROM=0 TO=1u');
%     fprintf(fp,'%s\n','.MEAS TRAN VMIN MIN V(4,3) FROM=0 TO=1u');
    fprintf(fp,'%s\n','.MEAS TRAN TRISE TRIG V(Voutp,Voutn) VAL=''VMIN+0.45*(VMAX-VMIN)'' RISE=1' );
    fprintf(fp,'%s\n','+ TARG V(Voutp,Voutn) VAL=''VMIN+0.5*(VMAX-VMIN)'' RISE=1');
    fprintf(fp,'%s\n','.MEAS SR PARAM=''(VMAX-VMIN)*0.05/TRISE'' ');
    fprintf(fp,'%s\n','.print tran  V(Voutp,Voutn)');
    fprintf(fp,'%s\n','.lib ''rf018.l'' TT');
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    tend=0.1e-6;
    h = 0.001e-9;
    system('C:\synopsys\Hspice_C-2009.09\BIN\hspice.exe  -i INT_rs.sp -o INT_rs -b');%jxm_hspice
    %system('C:\\synopsys\\Hspice2004.09\\BIN\\hspice.exe -i opamp.sp -o opamp -b');  

%     fp1=fopen('INT_rs.mt0','r+');
% 	for m=1:4
%         tline=fgets(fp1);
%     end
%     mt=fscanf(fp1,'%lf',4);
%     fclose(fp1);
%     SR=mt(4)*1.0E-6;   
%     pf2=SR;
    fp1=fopen('INT_rs.lis','r+'); 
    nt=0;
    while~feof(fp1)
        tline=fgets(fp1);
%         if(strncmp(tline,'            time',16)==1&&nt==0)
          if(strncmp(tline,'x',1)==1&&nt==0)
             for j=1:4 
                 tline=fgets(fp1);
                 stmp=fscanf(fp1,'%e%e',[1 2]);
             end
             for k=1
                 tmp =[0.0 0.0];
                 y(k,1:2)=tmp;
             end
            for k=2:100001
                tmp=fscanf(fp1,'%e%e',[1 2]);
                y(k,1:2)=tmp; 
            end

        end
    end
    fclose(fp1);
%     for k=1:size(ysp,1)
%        if (ysp(k,3)>u0*G0*(1-ovsht))
%          h=ysp(k,1)-ysp(k-1,1);
%          tssp=ysp(k-1,1)+h*((u0*G0*(1-ovsht)-ysp(k-1,3))/(ysp(k,3)-ysp(k-1,3)));
%          break;
%        end
%     end
%     ovshtsp=0.0; tovsp=ysp(end,1);
%     for k1=k:size(ysp,1)
%         if (ysp(k1,3)<ysp(k1-1,3))
%             ovshtsp=ysp(k1-1,3)/(u0*G0)-1;
%             tovsp=ysp(k1-1,1);
%             break;
%         end
%     end