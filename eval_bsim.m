function [ids,gm,gds,cgs]=eval_bsim(vgs,vds,vbs,W,L,PN)
global pathsp moslib crn Lb Ub gmid
%vgs=1.0; vds=3.0;
ids=[]; gm=[]; gds=[]; cgs=[];
fp=fopen('eval_mos.sp','w');
fprintf(fp,'%s\n','NMOS');
fprintf(fp,'%s\n','.options post=2 ingold=2');
%fprintf(fp,'%s\n','.include ''130nm_nominal.l'' ');
fprintf(fp,'%s\n',moslib);
% fprintf(fp,'%s\n','.LIB ''rf018.l'' TT');
%fprintf(fp,'%s\n\n','.LIB ''mm0355v.l'' TT');
% fprintf(fp,'%s\n\n','.lib ''crn90g_2d5_lk_v1d2p1.l'' TT');
%fprintf(fp,'%s\n','.LIB ''allen.l'' mos_allen');
if (PN>0)
    fprintf(fp,'%s%e%s%e\n','M1 out in 0 0 P18 W=',W*1e-6, ' L=',L*1e-6);
else
    fprintf(fp,'%s%e%s%e\n','M1 out in 0 0 N18 W=',W*1e-6, ' L=',L*1e-6);
end
fprintf(fp,'%s%e\n','vgss  in  0  ',vgs);
fprintf(fp,'%s%e\n','vdss  out 0  ',vds);
fprintf(fp,'%s\n\n','.op');	
fprintf(fp,'%s\n','.end');
fclose(fp);

%system('C:\\synopsys\\Hspice2004.09\\BIN\\hspice.exe -i eval_mos.sp -o eval_mos');
%system('C:\\synopsys\\Hspice_Z-2007.03\\BIN\\hspice.exe -i eval_mos.sp -o eval_mos');
system('D:\AnalogIC\HSPICE\Hspice_P-2019.06-SP1-1\WIN64\hspice.exe  -i eval_mos.sp -o eval_mos');
%pathsp1=strcat(pathsp,' -i eval_mos.sp -o eval_mos -b');
%system(pathsp1);
%将Hspice输出文件中的vgs ids输出到数组tmp中
fid=fopen('eval_mos.lis','r+');
while~feof(fid)
    str=fgets(fid);
    if(strncmp(str,' region',7))
        stmp=fscanf(fid,'%s',[1,2]);
        ids=str2num(stmp(3:end));
    end
    if(strncmp(str,'  vbs',5))
        stmp=fscanf(fid,'%s',[1,2]);
        vth=str2num(stmp(4:end));
    end
    if(strncmp(str,'  gam eff',9))
        stmp=fscanf(fid,'%s',[1,2]);
        gm=str2num(stmp(3:end));
        stmp=fscanf(fid,'%s',[1,2]);
        gds=str2num(stmp(4:end));
    end
    if(strncmp(str,'  cbtot',7))
        stmp=fscanf(fid,'%s',[1,2]);
        cgs=str2num(stmp(4:end));
        break
    end
end
fclose(fid);
