function [pf]=eval_ramp(Wn1,Wp1,Wn2,Wp2,Wn3,Wp3,L,Rb,C<PERSON>,Vdd,Vss,vindc)
global pathsp moslib crn Lb Ub gmid
fp=fopen('ringamp.sp','w+');
fprintf(fp,'%s\n','*CMOS Amplifer by inverters');
fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD numdgt=5 accurate');  %
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mp1 out1 ina nvdd nvdd PCH W=',Wp1,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mn1 out1 ina nvss nvss  NCH W=',Wn1,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mp2 out2+ out1 nvdd nvdd PCH W=',Wp2,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mn2 out2- out1 nvss nvss  NCH W=',Wn2,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%9.3f%s\n','Rbias  out2+  out2- ',Rb/1e3,'K');
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mp3 out out2+ nvdd nvdd PCH W=',Wp3,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%9.3f%s%s%6.3f%s\n','Mn3 out out2- nvss nvss  NCH W=',Wn3,'U ' ,'  L=',L,'U');
fprintf(fp,'%s%6.3f%s\n','CL out 0 ', CL,'pF');
fprintf(fp,'%s\n%s%s\n%s\n','.prot','.lib ',moslib,'.unprot');
fprintf(fp,'%s%6.3f\n','VDD nvdd 0 DC ', Vdd);
fprintf(fp,'%s%6.3f\n','VSS nvss 0 DC ', Vss);
fprintf(fp,'%s%6.3f%s\n','Vin ina 0 DC',vindc,' AC 1.0');
fprintf(fp,'%s\n','.OP');
fprintf(fp,'%s\n','.AC DEC 20 1 1000MEG');
fprintf(fp,'%s\n','.MEAS AC AD MAX VDB(out) FROM=1 TO=1000MEG');
fprintf(fp,'%s\n','.MEAS AC UGB TRIG AT=1 TARG VDB(out) VAL=0 CROSS=1');
fprintf(fp,'%s\n','.MEAS AC W3DB TRIG AT=1 TARG VDB(out) VAL=''AD-3'' CROSS=1');
fprintf(fp,'%s\n','.MEAS AC PHASE FIND VP(out) WHEN VDB(out)=0');
fprintf(fp,'%s\n','.MEAS AC PAMARGINE PARAM=''180+PHASE'' ');
fprintf(fp,'%s\n','.MEAS AC POW AVG(POWER) FROM=1 TO=UGB');
fprintf(fp,'%s\n','.pz V(out) Vin');
fprintf(fp,'%s\n','.END');
fclose(fp);
hspcmd=sprintf('%s %s',pathsp,'-i ringamp.sp -o ringamp -b');
system(hspcmd);
fp1=fopen('ringamp.ma0','r+');
    for m=1:4
        tline=fgets(fp1);
    end
    ma=fscanf(fp1,'%lf',6);
    fclose(fp1);
	Av=ma(1);    
	UGB=ma(2)*1.0E-6;  
	f3db=ma(3)*1.0E-3; 
	if(ma(4)<0)
	   PM=ma(5);  
    else
	   PM=ma(4);
    end
	Power=ma(6)*1.0e3; 
    pf(1)=Av; 
    pf(2)=UGB;
    pf(3)=PM;
end

