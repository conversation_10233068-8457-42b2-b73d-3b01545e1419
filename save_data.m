%% CSV文件保存MAT
% 读取CSV文件
data = readtable('Analog_processing_data.csv');  % 替换为你的文件名

% 创建带时间索引的结构体
simData = struct();

% 1. 存储时间向量 (作为单独字段)
simData.time = data.time_s_;  % 时间序列数据

% 2. 创建包含所有信号的数据表格 (排除时间列)
% 获取所有列名
allVars = data.Properties.VariableNames;
% 排除时间列（假设时间列名为'time_s'）
signalVars = setdiff(allVars, 'time_s_');

% 创建信号数据表格
simData.signals = data(:, signalVars);

% 3. 添加元数据（可选）
simData.metadata.fileName = 'Analog_processing_data_2us.csv';
simData.metadata.creationDate = datetime("now");
simData.metadata.variables = signalVars;

% 保存结构体到MAT文件
save('Analog_processing_data.mat', 'simData');

% 验证结构体内容
disp('结构体字段:');
disp(fieldnames(simData)');

disp('信号数据表头:');
disp(simData.signals.Properties.VariableNames);