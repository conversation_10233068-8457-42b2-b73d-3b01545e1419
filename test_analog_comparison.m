function comparison_results = test_analog_comparison()
%TEST_ANALOG_COMPARISON 测试模拟数据对比系统
%   VERSION: v1.0 - 理想模型与晶体管仿真数据对比测试系统
%
%   功能描述:
%   1. 测试数据提取功能是否正常工作
%   2. 生成理想模型与晶体管仿真数据的可视化对比
%   3. 计算各级模块的误差统计
%   4. 提供详细的对比分析报告
%
%   数据源:
%   - 理想模型数据：./test_data/pipeline_adc_raw_data_*.mat（自动查找最新文件）
%   - 晶体管仿真数据：./test_data/Analog_processing_data.mat
%
%   输出参数:
%       comparison_results - 对比分析结果结构体
%           .extracted_data - 提取的原始数据
%           .error_analysis - 误差分析统计
%           .visualization_info - 可视化信息

    fprintf('=== 模拟数据对比测试系统 v1.0 ===\n');
    
    %% 初始化输出结构体
    comparison_results = struct();
    comparison_results.test_success = false;
    comparison_results.error_message = '';
    comparison_results.extracted_data = [];
    comparison_results.error_analysis = [];
    comparison_results.visualization_info = [];
    
    %% 第一步：自动查找数据文件
    fprintf('\n[步骤1/5] 自动查找数据文件...\n');
    try
        % 查找理想模型数据文件（最新的pipeline_adc_raw_data文件）
        test_data_dir = './test_data';
        if ~exist(test_data_dir, 'dir')
            error('test_data文件夹不存在：%s', test_data_dir);
        end
        
        ideal_pattern = fullfile(test_data_dir, 'pipeline_adc_raw_data_*.mat');
        ideal_files = dir(ideal_pattern);
        
        if isempty(ideal_files)
            error('未找到理想模型数据文件：%s', ideal_pattern);
        end
        
        % 选择最新的文件
        [~, latest_idx] = max([ideal_files.datenum]);
        ideal_data_file = fullfile(test_data_dir, ideal_files(latest_idx).name);
        
        % 查找晶体管仿真数据文件
        sim_data_file = fullfile(test_data_dir, 'Analog_processing_data.mat');
        if ~exist(sim_data_file, 'file')
            error('晶体管仿真数据文件不存在：%s', sim_data_file);
        end
        
        fprintf('✓ 数据文件查找成功\n');
        fprintf('  理想模型数据: %s\n', ideal_files(latest_idx).name);
        fprintf('  晶体管仿真数据: %s\n', 'Analog_processing_data.mat');
        
    catch ME
        comparison_results.error_message = sprintf('数据文件查找失败: %s', ME.message);
        fprintf('✗ %s\n', comparison_results.error_message);
        return;
    end
    
    %% 第二步：调用数据提取函数
    fprintf('\n[步骤2/5] 调用数据提取函数...\n');
    try
        extracted_data = extract_pipeline_data(ideal_data_file, sim_data_file);
        comparison_results.extracted_data = extracted_data;
        
        fprintf('✓ 数据提取成功\n');
        fprintf('  提取周期数: %d\n', extracted_data.extraction_info.extract_cycles);
        
    catch ME
        comparison_results.error_message = sprintf('数据提取失败: %s', ME.message);
        fprintf('✗ %s\n', comparison_results.error_message);
        return;
    end
    
    %% 第三步：误差分析
    fprintf('\n[步骤3/5] 执行误差分析...\n');
    try
        error_analysis = perform_error_analysis(extracted_data);
        comparison_results.error_analysis = error_analysis;
        
        fprintf('✓ 误差分析完成\n');
        
    catch ME
        comparison_results.error_message = sprintf('误差分析失败: %s', ME.message);
        fprintf('✗ %s\n', comparison_results.error_message);
        return;
    end
    
    %% 第四步：数据可视化
    fprintf('\n[步骤4/5] 生成数据可视化...\n');
    try
        visualization_info = create_comparison_visualizations(extracted_data, error_analysis);
        comparison_results.visualization_info = visualization_info;
        
        fprintf('✓ 数据可视化完成\n');
        fprintf('  生成图表数: %d\n', visualization_info.total_figures);
        
    catch ME
        comparison_results.error_message = sprintf('数据可视化失败: %s', ME.message);
        fprintf('✗ %s\n', comparison_results.error_message);
        return;
    end
    
    %% 第五步：生成对比报告
    fprintf('\n[步骤5/5] 生成对比分析报告...\n');
    try
        generate_comparison_report(extracted_data, error_analysis);
        
        fprintf('✓ 对比分析报告生成完成\n');
        
        comparison_results.test_success = true;
        
    catch ME
        comparison_results.error_message = sprintf('报告生成失败: %s', ME.message);
        fprintf('✗ %s\n', comparison_results.error_message);
        return;
    end
    
    fprintf('\n=== 模拟数据对比测试完成 ===\n');
    if comparison_results.test_success
        fprintf('测试状态: 成功\n');
    else
        fprintf('测试状态: 失败\n');
    end
end

function error_analysis = perform_error_analysis(extracted_data)
%PERFORM_ERROR_ANALYSIS 执行理想模型与仿真数据的误差分析
%   计算各级模块的误差统计信息，包括绝对误差、相对误差、RMS误差等

    fprintf('执行误差分析...\n');
    
    stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                   'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
    
    error_analysis = struct();
    error_analysis.stage_errors = struct();
    error_analysis.overall_stats = struct();
    
    % 初始化整体统计
    all_absolute_errors = [];
    all_relative_errors = [];
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        % 获取理想信号和仿真信号
        ideal_signal = extracted_data.(stage_name).ideal_signal;
        sim_signal = extracted_data.(stage_name).sim_signal;
        
        if isempty(ideal_signal) || isempty(sim_signal)
            warning('跳过%s：数据为空', stage_name);
            continue;
        end
        
        % 确保信号长度一致
        min_length = min(length(ideal_signal), length(sim_signal));
        ideal_signal = ideal_signal(1:min_length);
        sim_signal = sim_signal(1:min_length);
        
        % 计算误差指标
        absolute_error = sim_signal - ideal_signal;
        
        % 避免除零错误：当理想信号接近零时使用绝对误差
        relative_error = zeros(size(absolute_error));
        non_zero_idx = abs(ideal_signal) > 1e-10;
        relative_error(non_zero_idx) = absolute_error(non_zero_idx) ./ ideal_signal(non_zero_idx);
        
        % 计算统计指标
        stage_stats = struct();
        stage_stats.mean_absolute_error = mean(abs(absolute_error));
        stage_stats.max_absolute_error = max(abs(absolute_error));
        stage_stats.rms_error = sqrt(mean(absolute_error.^2));
        stage_stats.mean_relative_error = mean(abs(relative_error(non_zero_idx)));
        stage_stats.max_relative_error = max(abs(relative_error(non_zero_idx)));
        stage_stats.correlation_coefficient = corrcoef(ideal_signal, sim_signal);
        stage_stats.correlation_coefficient = stage_stats.correlation_coefficient(1, 2);
        stage_stats.snr_db = 20 * log10(rms(ideal_signal) / rms(absolute_error));
        
        % 存储误差数据
        stage_stats.absolute_error = absolute_error;
        stage_stats.relative_error = relative_error;
        stage_stats.ideal_signal = ideal_signal;
        stage_stats.sim_signal = sim_signal;
        
        error_analysis.stage_errors.(stage_name) = stage_stats;
        
        % 累积整体统计数据
        all_absolute_errors = [all_absolute_errors; absolute_error];
        if ~isempty(relative_error(non_zero_idx))
            all_relative_errors = [all_relative_errors; relative_error(non_zero_idx)];
        end
        
        fprintf('  %s: MAE=%.6f, RMS=%.6f, SNR=%.1fdB\n', ...
                stage_name, stage_stats.mean_absolute_error, ...
                stage_stats.rms_error, stage_stats.snr_db);
    end
    
    % 计算整体统计
    error_analysis.overall_stats.mean_absolute_error = mean(abs(all_absolute_errors));
    error_analysis.overall_stats.max_absolute_error = max(abs(all_absolute_errors));
    error_analysis.overall_stats.rms_error = sqrt(mean(all_absolute_errors.^2));
    if ~isempty(all_relative_errors)
        error_analysis.overall_stats.mean_relative_error = mean(abs(all_relative_errors));
        error_analysis.overall_stats.max_relative_error = max(abs(all_relative_errors));
    else
        error_analysis.overall_stats.mean_relative_error = NaN;
        error_analysis.overall_stats.max_relative_error = NaN;
    end
    
    fprintf('整体误差统计: MAE=%.6f, RMS=%.6f\n', ...
            error_analysis.overall_stats.mean_absolute_error, ...
            error_analysis.overall_stats.rms_error);
end

function visualization_info = create_comparison_visualizations(extracted_data, error_analysis)
%CREATE_COMPARISON_VISUALIZATIONS 创建对比可视化图表
%   生成多个图表展示理想模型与仿真数据的对比结果

    fprintf('生成对比可视化图表...\n');
    
    stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                   'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
    
    visualization_info = struct();
    visualization_info.total_figures = 0;
    
    %% 图1：各级信号对比（3×3子图布局）
    figure('Name', 'Pipeline Stages: Ideal vs Simulation Comparison', ...
           'Position', [100, 100, 1500, 1000]);
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        
        if ~isfield(error_analysis.stage_errors, stage_name)
            continue;
        end
        
        stage_data = error_analysis.stage_errors.(stage_name);
        
        subplot(3, 3, i);
        
        % 绘制理想信号和仿真信号
        sample_indices = 1:length(stage_data.ideal_signal);
        plot(sample_indices, stage_data.ideal_signal, 'b-', 'LineWidth', 1.5, 'DisplayName', 'Ideal');
        hold on;
        plot(sample_indices, stage_data.sim_signal, 'r--', 'LineWidth', 1.5, 'DisplayName', 'Simulation');
        hold off;
        
        xlabel('Sample Index');
        ylabel('Voltage (V)');
        title(sprintf('%s (SNR: %.1f dB)', stage_name, stage_data.snr_db));
        legend('show', 'Location', 'best');
        grid on;
    end
    
    visualization_info.total_figures = visualization_info.total_figures + 1;
    
    %% 图2：误差分析图（绝对误差和相对误差）
    figure('Name', 'Error Analysis: Absolute and Relative Errors', ...
           'Position', [200, 200, 1500, 800]);
    
    % 子图1：各级绝对误差对比
    subplot(2, 1, 1);
    stage_mae = [];
    stage_labels = {};
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        if isfield(error_analysis.stage_errors, stage_name)
            stage_mae(end+1) = error_analysis.stage_errors.(stage_name).mean_absolute_error;
            stage_labels{end+1} = stage_name;
        end
    end
    
    bar(stage_mae, 'FaceColor', [0.2, 0.6, 0.8]);
    set(gca, 'XTickLabel', stage_labels);
    ylabel('Mean Absolute Error (V)');
    title('Mean Absolute Error by Pipeline Stage');
    grid on;
    
    % 子图2：各级SNR对比
    subplot(2, 1, 2);
    stage_snr = [];
    
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        if isfield(error_analysis.stage_errors, stage_name)
            stage_snr(end+1) = error_analysis.stage_errors.(stage_name).snr_db;
        end
    end
    
    bar(stage_snr, 'FaceColor', [0.8, 0.4, 0.2]);
    set(gca, 'XTickLabel', stage_labels);
    ylabel('SNR (dB)');
    title('Signal-to-Noise Ratio by Pipeline Stage');
    grid on;
    
    visualization_info.total_figures = visualization_info.total_figures + 1;
    
    %% 图3：散点图对比（理想 vs 仿真）
    figure('Name', 'Ideal vs Simulation Scatter Plot Analysis', ...
           'Position', [300, 300, 1500, 1000]);
    
    for i = 1:min(6, length(stage_names))  % 只显示前6级
        stage_name = stage_names{i};
        
        if ~isfield(error_analysis.stage_errors, stage_name)
            continue;
        end
        
        stage_data = error_analysis.stage_errors.(stage_name);
        
        subplot(2, 3, i);
        
        % 绘制散点图
        scatter(stage_data.ideal_signal, stage_data.sim_signal, 20, 'filled', 'Alpha', 0.6);
        hold on;
        
        % 绘制理想线（y=x）
        min_val = min([stage_data.ideal_signal; stage_data.sim_signal]);
        max_val = max([stage_data.ideal_signal; stage_data.sim_signal]);
        plot([min_val, max_val], [min_val, max_val], 'r-', 'LineWidth', 2);
        hold off;
        
        xlabel('Ideal Signal (V)');
        ylabel('Simulation Signal (V)');
        title(sprintf('%s (R=%.3f)', stage_name, stage_data.correlation_coefficient));
        grid on;
        axis equal;
    end
    
    visualization_info.total_figures = visualization_info.total_figures + 1;
    
    %% 图4：时钟域分组对比
    figure('Name', 'Clock Domain Comparison', ...
           'Position', [400, 400, 1400, 600]);
    
    clkh_stages = extracted_data.extraction_info.clkh_stages;
    clks_stages = extracted_data.extraction_info.clks_stages;
    
    % 子图1：CLKH域误差
    subplot(1, 2, 1);
    clkh_mae = [];
    clkh_labels = {};
    
    for i = 1:length(clkh_stages)
        stage_name = clkh_stages{i};
        if isfield(error_analysis.stage_errors, stage_name)
            clkh_mae(end+1) = error_analysis.stage_errors.(stage_name).mean_absolute_error;
            clkh_labels{end+1} = stage_name;
        end
    end
    
    if ~isempty(clkh_mae)
        bar(clkh_mae, 'FaceColor', [0.3, 0.7, 0.3]);
        set(gca, 'XTickLabel', clkh_labels);
        ylabel('Mean Absolute Error (V)');
        title('CLKH Clock Domain - Error Comparison');
        grid on;
    end
    
    % 子图2：CLKS域误差
    subplot(1, 2, 2);
    clks_mae = [];
    clks_labels = {};
    
    for i = 1:length(clks_stages)
        stage_name = clks_stages{i};
        if isfield(error_analysis.stage_errors, stage_name)
            clks_mae(end+1) = error_analysis.stage_errors.(stage_name).mean_absolute_error;
            clks_labels{end+1} = stage_name;
        end
    end
    
    if ~isempty(clks_mae)
        bar(clks_mae, 'FaceColor', [0.7, 0.3, 0.3]);
        set(gca, 'XTickLabel', clks_labels);
        ylabel('Mean Absolute Error (V)');
        title('CLKS Clock Domain - Error Comparison');
        grid on;
    end
    
    visualization_info.total_figures = visualization_info.total_figures + 1;
    
    fprintf('  生成%d个可视化图表\n', visualization_info.total_figures);
end

function generate_comparison_report(extracted_data, error_analysis)
%GENERATE_COMPARISON_REPORT 生成详细的对比分析报告
%   在命令行输出详细的数值对比报告

    fprintf('\n=== 理想模型与晶体管仿真数据对比分析报告 ===\n');
    
    % 基本信息
    fprintf('\n【基本信息】\n');
    fprintf('提取周期数: %d\n', extracted_data.extraction_info.extract_cycles);
    fprintf('跳过周期数: %d\n', extracted_data.extraction_info.skip_cycles);
    fprintf('稳定偏移: %d个样本点\n', extracted_data.extraction_info.stable_offset);
    
    % 时钟域分配
    fprintf('\n【时钟域分配】\n');
    fprintf('CLKH域: %s\n', strjoin(extracted_data.extraction_info.clkh_stages, ', '));
    fprintf('CLKS域: %s\n', strjoin(extracted_data.extraction_info.clks_stages, ', '));
    
    % 整体误差统计
    fprintf('\n【整体误差统计】\n');
    fprintf('平均绝对误差: %.6f V\n', error_analysis.overall_stats.mean_absolute_error);
    fprintf('最大绝对误差: %.6f V\n', error_analysis.overall_stats.max_absolute_error);
    fprintf('均方根误差: %.6f V\n', error_analysis.overall_stats.rms_error);
    if ~isnan(error_analysis.overall_stats.mean_relative_error)
        fprintf('平均相对误差: %.6f%%\n', error_analysis.overall_stats.mean_relative_error * 100);
        fprintf('最大相对误差: %.6f%%\n', error_analysis.overall_stats.max_relative_error * 100);
    end
    
    % 各级详细误差
    fprintf('\n【各级详细误差分析】\n');
    fprintf('%-8s | %-12s | %-12s | %-12s | %-10s | %-10s\n', ...
            'Stage', 'MAE (V)', 'MAX_AE (V)', 'RMS (V)', 'SNR (dB)', 'Corr');
    fprintf('%s\n', repmat('-', 1, 80));
    
    stage_names = fieldnames(error_analysis.stage_errors);
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        stage_data = error_analysis.stage_errors.(stage_name);
        
        fprintf('%-8s | %12.6f | %12.6f | %12.6f | %10.1f | %10.3f\n', ...
                stage_name, ...
                stage_data.mean_absolute_error, ...
                stage_data.max_absolute_error, ...
                stage_data.rms_error, ...
                stage_data.snr_db, ...
                stage_data.correlation_coefficient);
    end
    
    % 性能评估
    fprintf('\n【性能评估】\n');
    overall_snr = 20 * log10(1 / error_analysis.overall_stats.rms_error);
    fprintf('整体信噪比: %.1f dB\n', overall_snr);
    
    if error_analysis.overall_stats.mean_absolute_error < 0.001
        fprintf('误差水平: 优秀 (MAE < 1mV)\n');
    elseif error_analysis.overall_stats.mean_absolute_error < 0.01
        fprintf('误差水平: 良好 (MAE < 10mV)\n');
    elseif error_analysis.overall_stats.mean_absolute_error < 0.1
        fprintf('误差水平: 可接受 (MAE < 100mV)\n');
    else
        fprintf('误差水平: 需要改进 (MAE >= 100mV)\n');
    end
    
    fprintf('\n=== 报告生成完成 ===\n');
end 