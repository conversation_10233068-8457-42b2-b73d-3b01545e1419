*CMOS OPA
.OPTIONs post=2 measfall=0 ingold=2 NOMOD numdgt=7 accurate
.subckt two_stage_opamp_CTCMFB AVDD AVSS Vcm Vin Vip Voutn Voutp 
M0 net92 net021 AVSS AVSS N18 W=1501.00000U L=  0.30000U
M1 net90 Vip net92 net92 N18 W=  1.42944U L=  0.30000U
M2 net96 Vin net92 net92 N18 W=  1.42944U L=  0.30000U
M3 net90 vcmfb AVDD AVDD P18 W=  5.00000U L=  0.30000U
M4 net96 vcmfb AVDD AVDD P18 W=  5.00000U L=  0.30000U
M5 Voutp net90 AVDD AVDD P18 W=  6.42789U L=  0.30000U
M6 Voutn net96 AVDD AVDD P18 W=  6.42789U L=  0.30000U
M7 Voutn net021 AVSS AVSS N18 W=3022.85682U L=  0.30000U
M8 Voutp net021 AVSS AVSS N18 W=3022.85682U L=  0.30000U
M9 net027 net021 AVSS AVSS N18 W=2853.94250U L=  0.30000U
M10 vcmfb net048 net027 net027 N18 W=  0.12457U L=  0.30000U
M11 net028 Vcm net027 net027 N18 W=  0.12457U L=  0.30000U
M12 vcmfb vcmfb AVDD AVDD P18 W= 10.10724U L=  0.30000U
M13 net028 net028 AVDD AVDD P18 W= 10.10724U L=  0.30000U
M14 net021 net021 AVSS AVSS N18 W=1581.06389U L=  0.30000U
R1 Voutp net048 100K
R2 Voutn net048 100K
C1 Voutn net048 260f
C2 Voutp net048 260f
Cc1 net038 Voutn   0.30000P
Cc2 Voutp net037   0.30000P
Rc1 net96 net038  2.40563K
Rc2 net037 net90  2.40563K
IBIAS AVDD net021 30.00000U
.LIB 'ms018_v1p9.lib' TT
.ENDS
VDD AVDD 0 DC   1.800
VSS AVSS 0 DC   0.000
CL1 Voutn 0   0.400P
CL2 Voutp 0   0.400P
Vcm vcm 0 DC 0.9 
X1 AVDD AVSS Vcm Vin Vip Voutn Voutp two_stage_opamp_CTCMFB
Vind  101  0  0  AC 1 
EIN+ vip vincm  101  0  1
EIN- vincm vin  101  0  1
Vincm   vincm  0  DC  0.9 
.OP
.AC DEC 20 1 1000MEG
.MEAS AC AD MAX VDB(Voutp) FROM=1 TO=1000MEG
.MEAS AC UGB TRIG AT=1 TARG VDB(Voutp) VAL=0 CROSS=1
.MEAS AC W3DB TRIG AT=1 TARG VDB(Voutp) VAL='AD-3' CROSS=1
.MEAS AC PHASE FIND VP(Voutp) WHEN VDB(Voutp)=0
.MEAS AC PAMARGINE PARAM='180+PHASE' 
.MEAS AC POW AVG(POWER) FROM=1 TO=UGB
.END
