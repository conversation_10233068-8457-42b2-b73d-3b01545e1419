function [perf_metrics] = analyze_performance(ideal_adc_output, transistor_sim_output, opt_adc_output, params, t_sampled, clks, clkh)
% ANALYZE_PERFORMANCE 性能指标分析模块（增强版）
%   采用两阶段处理策略：
%   1. 使用完整样本进行事件驱动处理（保持时序精度）
%   2. 智能提取1024个稳定状态样本进行FFT分析（满足动态特性要求）
%
% 输入:
%   ideal_adc_output - 理想ADC输出（完整样本）
%   transistor_sim_output - 仿真ADC输出（完整样本）
%   opt_adc_output - 优化ADC输出（完整样本）
%   params - 参数结构体
%   t_sampled - 时间向量（完整）
%   clks, clkh - 时钟信号（可选，用于智能采样）
%
% 输出:
%   perf_metrics - 性能指标结构体

try
    fprintf('\n=== 增强性能分析：智能动态特性采样 ===\n');
    
    % 验证输入参数
    if nargin < 7
        % 如果缺少时钟信号，生成默认时钟用于采样
        fprintf('警告: 缺少时钟信号，将生成默认时钟进行采样\n');
        [~, ~, clks, clkh] = generate_adaptive_timebase(params);
    end
    
    % 显示完整样本信息
    fprintf('完整样本统计:\n');
    fprintf('  理想ADC样本数: %d\n', length(ideal_adc_output));
    fprintf('  仿真ADC样本数: %d\n', length(transistor_sim_output));
    fprintf('  优化ADC样本数: %d\n', length(opt_adc_output));
    
    % 第一阶段：智能提取FFT分析样本
    fprintf('\n--- 阶段1: 智能动态特性采样 ---\n');
    
    % 提取理想ADC的FFT分析样本
    [ideal_fft_samples, ideal_sampling_info] = extract_dynamic_test_samples(...
        ideal_adc_output, t_sampled, clks, clkh, params);
    
    % 提取仿真ADC的FFT分析样本
    [sim_fft_samples, sim_sampling_info] = extract_dynamic_test_samples(...
        transistor_sim_output, t_sampled, clks, clkh, params);
    
    % 提取优化ADC的FFT分析样本
    [opt_fft_samples, opt_sampling_info] = extract_dynamic_test_samples(...
        opt_adc_output, t_sampled, clks, clkh, params);
    
    % 验证采样质量
    min_quality = min([ideal_sampling_info.quality_score, ...
                      sim_sampling_info.quality_score, ...
                      opt_sampling_info.quality_score]);
    
    if min_quality < 6.0
        warning('采样质量偏低 (%.1f/10)，可能影响FFT分析精度', min_quality);
    end
    
    % 第二阶段：FFT动态特性分析
    fprintf('\n--- 阶段2: FFT动态特性分析 ---\n');
    
    % 计算有效采样率（基于智能采样的实际采样率）
    fs_effective = calculate_effective_sampling_rate(ideal_sampling_info, params);
    
    % 理想ADC性能指标
    fprintf('分析理想ADC动态特性...\n');
    [ideal_sndr, ideal_enob, ideal_snr, ideal_sfdr, ideal_hd_values] = ...
        calculate_sndr(ideal_fft_samples, fs_effective, params.f_in);
    
    % 仿真ADC性能指标
    fprintf('分析仿真ADC动态特性...\n');
    [sim_sndr, sim_enob, sim_snr, sim_sfdr, sim_hd_values] = ...
        calculate_sndr(sim_fft_samples, fs_effective, params.f_in);
    
    % 优化后模型的性能指标
    fprintf('分析优化ADC动态特性...\n');
    [opt_sndr, opt_enob, opt_snr, opt_sfdr, opt_hd_values] = ...
        calculate_sndr(opt_fft_samples, fs_effective, params.f_in);
    
    % 第三阶段：性能对比分析
    fprintf('\n--- 阶段3: 性能对比分析 ---\n');
    
    % 计算性能差异
    sndr_diff_sim = abs(ideal_sndr - sim_sndr);
    sndr_diff_opt = abs(ideal_sndr - opt_sndr);
    improvement = sndr_diff_sim - sndr_diff_opt;
    
    fprintf('性能对比结果:\n');
    fprintf('  理想ADC - SNDR: %.2f dB, SNR: %.2f dB, ENOB: %.2f bits\n', ...
            ideal_sndr, ideal_snr, ideal_enob);
    fprintf('  仿真ADC - SNDR: %.2f dB, SNR: %.2f dB, ENOB: %.2f bits\n', ...
            sim_sndr, sim_snr, sim_enob);
    fprintf('  优化ADC - SNDR: %.2f dB, SNR: %.2f dB, ENOB: %.2f bits\n', ...
            opt_sndr, opt_snr, opt_enob);
    fprintf('  优化改进: %.2f dB (SNDR)\n', improvement);
    
    % 收集所有性能指标到结构体中
    perf_metrics = struct();
    
    % 理想ADC指标
    perf_metrics.ideal.sndr = ideal_sndr;
    perf_metrics.ideal.snr = ideal_snr;
    perf_metrics.ideal.enob = ideal_enob;
    perf_metrics.ideal.sfdr = ideal_sfdr;
    perf_metrics.ideal.hd_values = ideal_hd_values;
    perf_metrics.ideal.sampling_info = ideal_sampling_info;
    
    % 仿真ADC指标
    perf_metrics.sim.sndr = sim_sndr;
    perf_metrics.sim.snr = sim_snr;
    perf_metrics.sim.enob = sim_enob;
    perf_metrics.sim.sfdr = sim_sfdr;
    perf_metrics.sim.hd_values = sim_hd_values;
    perf_metrics.sim.sampling_info = sim_sampling_info;
    
    % 优化后ADC指标
    perf_metrics.opt.sndr = opt_sndr;
    perf_metrics.opt.snr = opt_snr;
    perf_metrics.opt.enob = opt_enob;
    perf_metrics.opt.sfdr = opt_sfdr;
    perf_metrics.opt.hd_values = opt_hd_values;
    perf_metrics.opt.sampling_info = opt_sampling_info;
    
    % 综合分析结果
    perf_metrics.analysis.improvement = improvement;
    perf_metrics.analysis.effectiveness = min(100, max(0, improvement/sndr_diff_sim*100));
    perf_metrics.analysis.fs_effective = fs_effective;
    perf_metrics.analysis.sampling_quality = min_quality;
    
    % 相干采样验证
    perf_metrics.analysis.coherent_sampling = ideal_sampling_info.coherent_sampling;
    perf_metrics.analysis.coherent_error = ideal_sampling_info.coherent_error;
    
    % 保存FFT分析样本（用于进一步分析）
    perf_metrics.fft_samples.ideal = ideal_fft_samples;
    perf_metrics.fft_samples.sim = sim_fft_samples;
    perf_metrics.fft_samples.opt = opt_fft_samples;
    
    fprintf('\n=== 增强性能分析完成 ===\n');
    fprintf('综合效果评估: %.1f%% (采样质量: %.1f/10)\n', ...
        perf_metrics.analysis.effectiveness, perf_metrics.analysis.sampling_quality);
    
catch ME
    fprintf('错误: 增强性能分析失败 - %s\n', ME.message);
    
    % 返回基础性能指标结构体
    perf_metrics = struct();
    perf_metrics.error = ME.message;
    perf_metrics.analysis.effectiveness = 0;
    perf_metrics.analysis.sampling_quality = 0;
end

end

function fs_effective = calculate_effective_sampling_rate(sampling_info, params)
%CALCULATE_EFFECTIVE_SAMPLING_RATE 计算有效采样率
%   基于智能采样的实际时间分布计算有效采样率

    if isfield(sampling_info, 'sample_indices') && length(sampling_info.sample_indices) > 1
        % 基于实际采样点的时间间隔计算
        time_span = max(sampling_info.sample_indices) - min(sampling_info.sample_indices);
        num_samples = length(sampling_info.sample_indices);
        
        % 估算有效采样率
        fs_effective = (num_samples - 1) * params.fs / time_span;
        
        % 限制在合理范围内
        fs_effective = min(fs_effective, params.fs);
        fs_effective = max(fs_effective, params.fs / 4);
    else
        % 回退到标准采样率
        fs_effective = params.fs;
    end
    
    fprintf('有效采样率: %.2f MHz (标准: %.2f MHz)\n', ...
        fs_effective/1e6, params.fs/1e6);
end 