%
% Evaluation of 2-stage opamp with interp1L1conventional nulling resistor compensation
%
function [pf]=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,Cc,Ibias,Vdd,Vss,CL,Rc)    
    global  pathsp moslib ovsht t u0
    fp=fopen('opamp.sp','w+');
    fprintf(fp,'%s\n','*CMOS OPA');
    fprintf(fp,'%s\n','.OPTIONs post=2 measfall=0 ingold=2 NOMOD numdgt=7 accurate');  %
    fprintf(fp,'%s\n','.subckt two_stage_opamp_CTCMFB AVDD AVSS Vcm Vin Vip Voutn Voutp ');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M0 net92 net021 AVSS AVSS N18 W=',W0,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M1 net90 Vip net92 net92 N18 W=',W1,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M2 net96 Vin net92 net92 N18 W=',W1,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M3 net90 vcmfb AVDD AVDD P18 W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M4 net96 vcmfb AVDD AVDD P18 W=',W3,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M5 Voutp net90 AVDD AVDD P18 W=',W6,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M6 Voutn net96 AVDD AVDD P18 W=',W6,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M7 Voutn net021 AVSS AVSS N18 W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M8 Voutp net021 AVSS AVSS N18 W=',W7,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M9 net027 net021 AVSS AVSS N18 W=',W9,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M10 vcmfb net048 net027 net027 N18 W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M11 net028 Vcm net027 net027 N18 W=',W10,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M12 vcmfb vcmfb AVDD AVDD P18 W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M13 net028 net028 AVDD AVDD P18 W=',W12,'U ' ,'L=',L,'U');
    fprintf(fp,'%s%9.5f%s%s%9.5f%s\n','M14 net021 net021 AVSS AVSS N18 W=',W14,'U ' ,'L=',L,'U');
    fprintf(fp,'%s\n','R1 Voutp net048 100K');
    fprintf(fp,'%s\n','R2 Voutn net048 100K');
    fprintf(fp,'%s\n','C1 Voutn net048 260f');
    fprintf(fp,'%s\n','C2 Voutp net048 260f');
    fprintf(fp,'%s%9.5f%s\n','Cc1 net038 Voutn ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Cc2 Voutp net037 ',Cc,'P');
    fprintf(fp,'%s%9.5f%s\n','Rc1 net96 net038',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','Rc2 net037 net90',Rc/1e3,'K');
    fprintf(fp,'%s%9.5f%s\n','IBIAS AVDD net021',Ibias*1e6,'U');  
    fprintf(fp,'%s\n',moslib);
    fprintf(fp,'%s\n','.ENDS');
    fprintf(fp,'%s%7.3f\n','VDD AVDD 0 DC ', Vdd);
    fprintf(fp,'%s%7.3f\n','VSS AVSS 0 DC ', Vss);
    fprintf(fp,'%s%7.3f%s\n','CL1 Voutn 0 ',CL,'P');
    fprintf(fp,'%s%7.3f%s\n','CL2 Voutp 0 ',CL,'P');
    fprintf(fp,'%s\n','Vcm vcm 0 DC 0.9 ');

    fprintf(fp,'%s\n','X1 AVDD AVSS Vcm Vin Vip Voutn Voutp two_stage_opamp_CTCMFB');
    fprintf(fp,'%s\n','Vind  101  0  0  AC 1 ');
    fprintf(fp,'%s\n','EIN+ vip vincm  101  0  1'); % 压控电压源
    fprintf(fp,'%s\n','EIN- vincm vin  101  0  1');
    fprintf(fp,'%s\n','Vincm   vincm  0  DC  0.9 ');
    fprintf(fp,'%s\n','.OP');
    fprintf(fp,'%s\n','.AC DEC 20 1 1000MEG');
    fprintf(fp,'%s\n','.MEAS AC AD MAX VDB(Voutp) FROM=1 TO=1000MEG');
    fprintf(fp,'%s\n','.MEAS AC UGB TRIG AT=1 TARG VDB(Voutp) VAL=0 CROSS=1');
    fprintf(fp,'%s\n','.MEAS AC W3DB TRIG AT=1 TARG VDB(Voutp) VAL=''AD-3'' CROSS=1');
    fprintf(fp,'%s\n','.MEAS AC PHASE FIND VP(Voutp) WHEN VDB(Voutp)=0');
    fprintf(fp,'%s\n','.MEAS AC PAMARGINE PARAM=''180+PHASE'' ');
    fprintf(fp,'%s\n','.MEAS AC POW AVG(POWER) FROM=1 TO=UGB');
    fprintf(fp,'%s\n','.END');
    fclose(fp);
    hspcmd=sprintf('%s %s',pathsp,'-i opamp.sp -o opamp -b');
    system(hspcmd);
    %system('C:\\synopsys\\Hspice2004.09\\BIN\\hspice.exe -i opamp.sp -o opamp -b');     
	fp1=fopen('opamp.ma0','r+');
    for m=1:4
        tline=fgets(fp1);
    end
    ma=fscanf(fp1,'%lf',6);
    fclose(fp1);
	Av=ma(1);    
	UGB=ma(2)*1.0E-6;  
	f3db=ma(3)*1.0E-3; 
	if(ma(4)<0)
	   PM=ma(5);  
    else
	   PM=ma(4);
    end
	Power=ma(6)*1.0e3; 
    pf(1)=Av; 
    pf(2)=UGB;
    pf(3)=PM;
    end

