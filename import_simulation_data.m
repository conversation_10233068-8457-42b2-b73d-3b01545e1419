function [final_output, pipeline_stages] = import_simulation_data(data_file) 
% IMPORT_SIMULATION_DATA 导入流水线ADC仿真数据
%   [final_output, pipeline_stages] = IMPORT_SIMULATION_DATA(data_file)
%   从.mat文件中读取dataTable表格数据，提取流水线级输出数据
%   注意：时间向量和时钟信号现在通过generate_adaptive_timebase()生成
%
% 输入:
%   data_file - 包含仿真数据的.mat文件路径
%
% 输出:
%   final_output - 仿真ADC最终输出数据
%   pipeline_stages - 包含各级流水线ADC输出的结构体数组

try
    % 检查文件是否存在
    if ~exist(data_file, 'file')
        error('找不到仿真数据文件: %s', data_file);
    end
    
    % 加载仿真数据
    sim_data = load(data_file);
    fprintf('成功加载数据文件: %s\n', data_file);
    
    % 检查dataTable是否存在
    if ~isfield(sim_data, 'dataTable')
        error('数据文件中找不到dataTable表格');
    end
    
    if ~istable(sim_data.dataTable)
        error('dataTable不是有效的表格格式');
    end
    
    % 获取dataTable
    data_table = sim_data.dataTable;
    
    % 检查表格大小
    fprintf('表格大小: %d行 x %d列\n', height(data_table), width(data_table));
    
    % 检查必需列是否存在
    required_columns = {'Pipeline_STAGE8', 'Pipeline_STAGE7', 'Pipeline_STAGE6', ...
                        'Pipeline_STAGE5', 'Pipeline_STAGE4', 'Pipeline_STAGE3', ...
                        'Pipeline_STAGE2', 'Pipeline_STAGE1', 'Final_Output'};
    
    missing_columns = required_columns(~ismember(required_columns, data_table.Properties.VariableNames));
    if ~isempty(missing_columns)
        error('表格缺少以下列: %s', strjoin(missing_columns, ', '));
    end
    
    % 提取最终输出数据
    final_output = data_table.Final_Output;
    
    % 提取流水线各级输出
    pipeline_stages = struct();
    stage_columns = {'Pipeline_STAGE1', 'Pipeline_STAGE2', 'Pipeline_STAGE3', 'Pipeline_STAGE4', ...
                     'Pipeline_STAGE5', 'Pipeline_STAGE6', 'Pipeline_STAGE7', 'Pipeline_STAGE8'};
    
    % 提取各级输出
    for i = 1:length(stage_columns)
        stage_name = sprintf('stage%d', i);
        pipeline_stages.(stage_name) = data_table.(stage_columns{i});
        fprintf('- 成功导入%s数据\n', stage_columns{i});
    end
    
    % 输出数据导入信息
    fprintf('成功导入仿真数据：\n');
    fprintf('- 最终输出数据点数: %d\n', length(final_output));
    fprintf('- 流水线级数: %d\n', length(stage_columns));
    fprintf('注意: 时间向量和时钟信号现通过generate_adaptive_timebase()生成\n');
    
catch ME
    % 详细的错误信息
    fprintf('数据导入错误详情:\n');
    fprintf('  消息: %s\n', ME.message);
    fprintf('  发生在: %s, 行 %d\n', ME.stack(1).name, ME.stack(1).line);
    error('数据导入错误: %s', ME.message);
end
end 