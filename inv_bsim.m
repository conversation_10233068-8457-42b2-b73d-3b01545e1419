%
% Solve W from (Ids,Vgs,Vds,Vbs,L) with BSIM model equ.
%
function [W1]=inv_bsim(ids,vgs,vds,vbs,L,PN)
global Lb Ub
Lb=0.18;Ub=100;
%ids=9.9880e-005;
%vgs=1.2; vds=3.0; vbs=0; L=1.0; W0=4; 
%Vov=vgs-VT;
%W0=L*2*abs(ids)/(UCox*Vov^2)/1e-6;
%W0=min(W0,180);
[ids0,gm0,gds0,cgs0]=eval_bsim(vgs,vds,vbs,5,L,PN); 
[ids1,gm1,gds1,cgs1]=eval_bsim(vgs,vds,vbs,10,L,PN);
W0=5+5*(ids-ids0)/(ids1-ids0);
W0=min(W0,0.95*Ub); W0=max(W0,1.5*Lb);
Wdel=1000; k=0; 
while (abs(Wdel)>1e-2*W0)
    k=k+1;
    W0=min(W0,0.95*Ub);
    W0=max(W0,1.5*Lb);
    [ids0 gm0 gds0 cgs1]=eval_bsim(vgs,vds,vbs,W0,L,PN); 
    [ids1 gm1 gds1 cgs1]=eval_bsim(vgs,vds,vbs,W0+0.01*W0,L,PN);
    didw=(ids1-ids0)/(0.01*W0);
    W1=W0+(ids-ids0)/didw;
    Wdel=W1-W0;
    W0=W1;
    if (k>20) break; 
    else continue
    end
end


