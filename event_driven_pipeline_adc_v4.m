function [correction_output_struct, processing_info, correction_metrics, raw_analog_data] = ...
    event_driven_pipeline_adc_v4(Vin_p, Vin_n, params, rel_error_params)
%EVENT_DRIVEN_PIPELINE_ADC_V4 事件驱动流水线ADC主模型 (v4版本)
%   VERSION HISTORY:
%   v1-v3: 复杂的事件系统、握手信号、标签对齐机制
%   v4: 回归实际电路设计思路的完全重构版本
%   v4.3: 直接输出结构体数据，移除兼容性处理
%
%   v4版本核心特性:
%   1. 调用新的generate_timebase.m生成固定时间步长信号
%   2. 调用event_processing_core_v6.m进行简化事件驱动处理
%   3. 调用digital_correction_v4.m进行时延对齐和RSD校正
%   4. 移除复杂的有效性标记和握手信号机制
%   5. 基于数据矩阵的统一处理架构
%   6. 直接输出结构体格式数据
%
%   输入参数:
%       Vin_p, Vin_n - 差分输入信号 (如果为空，自动生成)
%       params - ADC参数结构体
%       rel_error_params - 相对误差参数 (可选，默认为零误差)
%
%   输出参数:
%       correction_output_struct - 校正输出结构体
%           .time - 时间向量
%           .signals - 信号结构体数组 (1个模拟 + 10个数字)
%       processing_info - v6事件处理统计信息
%       correction_metrics - v4数字校正统计信息
%       raw_analog_data - 各级原始模拟数据输出

    fprintf('=== 事件驱动流水线ADC v4.3 (直接结构体输出) ===\n');
    
    %% 参数验证和初始化
    if nargin < 3 || isempty(params)
        error('至少需要提供params参数');
    end
    
    % 设置默认误差参数（零误差）- 新格式：8×3矩阵
    if nargin < 4 || isempty(rel_error_params)
        % 新格式：8×3矩阵 [gain_error, cap_mismatch, offset_error]
        rel_error_params = zeros(params.num_stages, 3);
        fprintf('使用零误差参数进行理想验证 (8×3格式: [gain_error, cap_mismatch, offset_error])\n');
    else
        % 验证误差参数矩阵格式
        [num_stages_params, num_error_types] = size(rel_error_params);
        if num_stages_params ~= params.num_stages
            error('rel_error_params行数错误！必须为%d行（对应%d级流水线），当前为%d行', ...
                  params.num_stages, params.num_stages, num_stages_params);
        end
        if num_error_types ~= 3
            error('rel_error_params列数错误！必须为3列 [gain_error, cap_mismatch, offset_error]，当前为%d列', num_error_types);
        end
        fprintf('使用外部提供的误差参数矩阵 (%d×%d)\n', num_stages_params, num_error_types);
    end
    
    %% 第一步：生成固定时间步长信号和时钟
    fprintf('\n=== 第一步：生成固定时间步长信号和时钟 ===\n');
    
    % 通过generate_timebase.m生成时间基准和时钟信号
    [t, ~, clks, clkh, digital_data_struct, analog_data_struct] = generate_timebase(params);
    num_samples = length(t);
    fprintf('使用generate_timebase生成时间基准和时钟信号: %d时间点\n', num_samples);
    
    % 验证或生成差分输入信号
    if isempty(Vin_p) || isempty(Vin_n)
        error('验证流程中必须提供外部生成的差分输入信号 Vin_p 和 Vin_n');
    end
    
    % 验证输入信号长度一致性
    if length(Vin_p) ~= num_samples || length(Vin_n) ~= num_samples
        error('输入信号长度与时间基准不一致: Vin_p=%d, Vin_n=%d, 时间基准=%d', ...
              length(Vin_p), length(Vin_n), num_samples);
    end
    
    fprintf('信号验证: %d时间点, %.1f μs仿真时长\n', num_samples, (max(t)-min(t))*1e6);
    
    %% 第二步：v6事件驱动处理
    fprintf('\n=== 第二步：v6事件驱动处理 ===\n');

    [digital_data_struct, analog_data_struct, processing_info] = ...
        event_processing_core_v6(Vin_p, Vin_n, t, clks, clkh, ...
                                digital_data_struct, analog_data_struct, ...
                                rel_error_params, params);

    % 保存原始模拟数据用于测试
    raw_analog_data = analog_data_struct;

    %% 数据保存：保存事件驱动核心处理后的原始数据
    fprintf('\n=== 数据保存：保存事件驱动核心原始数据 ===\n');
    
    try
        % 确保test_data文件夹存在
        test_data_dir = './test_data';
        if ~exist(test_data_dir, 'dir')
            mkdir(test_data_dir);
            fprintf('创建test_data文件夹: %s\n', test_data_dir);
        end
        
        % 生成文件名
        save_filename = fullfile(test_data_dir, sprintf('pipeline_adc_raw_data.mat'));
        
        % 创建保存数据结构
        pipeline_raw_data = struct();
        
        % 保存数字信号原始数据（18位独立存储）
        pipeline_raw_data.digital_data_struct = digital_data_struct;
        
        % 保存模拟信号原始数据（9条线路独立存储）
        pipeline_raw_data.analog_data_struct = analog_data_struct;
        
        % 保存共同的时间索引
        pipeline_raw_data.time_vector = t;
        
        % 保存处理信息和参数
        pipeline_raw_data.processing_info = processing_info;
        pipeline_raw_data.params = params;
        pipeline_raw_data.rel_error_params = rel_error_params;
        
        % 添加元数据
        pipeline_raw_data.metadata = struct();
        pipeline_raw_data.metadata.creation_time = datetime('now');
        pipeline_raw_data.metadata.num_samples = num_samples;
        pipeline_raw_data.metadata.simulation_duration_us = (max(t)-min(t))*1e6;
        pipeline_raw_data.metadata.digital_signals_count = length(digital_data_struct.signals);
        pipeline_raw_data.metadata.analog_signals_count = length(analog_data_struct.signals);
        pipeline_raw_data.metadata.description = '事件驱动流水线ADC原始数据 - 事件驱动核心处理后的完整数据集';
        
        % 保存到MAT文件
        save(save_filename, 'pipeline_raw_data');
        
        fprintf('✓ 原始数据保存成功:\n');
        fprintf('  文件名: %s\n', save_filename);
        
    catch save_error
        error('数据保存失败: %s', save_error.message);
    end

    %% 第三步：v4数字校正（时延对齐+RSD）
    fprintf('\n=== 第三步：v4数字校正（时延对齐+RSD） ===\n');

    [correction_output_struct, correction_metrics] = ...
        digital_correction_v4(digital_data_struct, params);
    
    %% 增强校正统计信息
    correction_metrics.output_struct_signals = length(correction_output_struct.signals);
    correction_metrics.time_vector_length = length(correction_output_struct.time);
    
    % 获取模拟输出范围（第1个信号）
    if ~isempty(correction_output_struct.signals)
        analog_values = correction_output_struct.signals(1).values;
        correction_metrics.analog_output_range = [min(analog_values), max(analog_values)];
    else
        correction_metrics.analog_output_range = [NaN, NaN];
    end
    
    fprintf('校正输出结构体生成完成:\n');
    fprintf('  结构体输出: %d个信号\n', correction_metrics.output_struct_signals);
    fprintf('  模拟输出范围: [%.6f, %.6f] V\n', ...
            correction_metrics.analog_output_range(1), correction_metrics.analog_output_range(2));
    
    fprintf('\n=== 事件驱动流水线ADC v4.3处理完成 ===\n');
    fprintf('整体性能: 校正输出%.1f%%, 处理效率%.1f%%\n', ...
            100*correction_metrics.processing_efficiency, ...
            100*correction_metrics.processing_efficiency);
    
end
