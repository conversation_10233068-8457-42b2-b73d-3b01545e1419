function [W0,W1,W3,W6,W7,W9,W10,W12,W14,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,Rc]=op2sizing(gm1,UGB,SR,K)
global Vdd Vss VTn CL L gmid Vinmin icompen Itot
%
Vout0=0.5*(Vdd+Vss);
vsd6=Vdd-Vout0; vds7=Vout0-Vss; 
Vin1=Vout0; Vin2=Vout0; vbs = 0;
Cc=gm1*1e6/UGB;    % C: pF;  UGB Mega rad/s
Ibias=SR*Cc*1e-6; ids1=0.5*Ibias;
%  gmid1 =gm1/ids1; if (gm1/ids1>12) ids1=gm1/12; end
ids6=SR*(CL+Cc)*1e-6;
if (icompen==1)    % zero nulling compensation schme
    gm6=K*UGB*CL*1e-6;
    gmid6 = gm6/ids6;
    if (gm6/ids6>9) ids6=gm6/9; end
else    % pz cancellation compensation
    omegat6=K*UGB*(1+CL/Cc);   %Mega rad/s
    %if (omegat6<4e3) continue; end
    if (omegat6<4e3) 
        icompen=1;  gm6=K*UGB*CL*1e-6; 
    else
        gmid6=interp1(gmid(:,4)./gmid(:,9),gmid(:,4),omegat6*1e6);
        gm6=gmid6*ids6;
    end
end
[vgs6,W6]=inv_bsim1(-ids6,gm6,-vsd6,0,L,1);
vsg3=0.6; vsd3=-vgs6;
W3=inv_bsim(-ids1,-vsg3,-vsd3,0,L,1);
vgs1=(0.1+VTn); Vs1=Vin1-vgs1; vds1=Vdd-vsd3-Vs1; vgs1a=1.1*vgs1;
while abs(vgs1/vgs1a-1)>0.5e-2
    [vgs1, W1]=inv_bsim1(Ibias/2,gm1,vds1,0,L,-1);
    Vs1=Vin1-vgs1; vds1=Vdd-vsd3-Vs1; vgs1a=vgs1;
end
vds0=Vs1-Vss;
Vov0=Vinmin-Vss-vgs1; vgs0=Vov0+VTn;
W0=inv_bsim(Ibias,vgs0,vds0,0,L,-1);
if (icompen==1) Rc=1/gm6; else Rc=(1+CL/Cc)/gm6; end
W7=inv_bsim(ids6,vgs0,vds7,0,L,-1);
W14=inv_bsim(Ibias,vgs0,vgs0,0,L,-1);
ids9=Ibias;
W9=inv_bsim(ids9,vgs0,vds0,vbs,L,-1);
W10=inv_bsim(ids9/2,vgs1,Vdd-vsg3-Vs1,vbs,L,-1);
W12=inv_bsim(-ids9/2,-vsg3,-vsg3,vbs,L,1);
Itot=3*Ibias+2*ids6;
%K=gm6/(CL*UGB*1e-6);
%pf=eval_opamp(W0,W1,W3,W6,W7,W9,W10,W12,W14,L,L1,L6,Cc,Ibias,Vdd,Vss,CL,Rc);
%[SR2,tssp,ysp]=eval_int1(pw1,pw2,Ts,T1,Tsim1,Vdd,Vss,Cc,Rc,W0,W1,W3,W6,W7,W9,W10,W12,W14,Ws,Wsp,Ls,L,L1,L6,Ibias,Cs1,Cs2,Ci1,Ci2,Co);
