function [opt_rel_error_matrix, opt_adc_output] = optimize_error_model(Vin_p, Vin_n, params, transistor_sim_output, sample_indices)
% OPTIMIZE_ERROR_MODEL 优化相对误差模型以拟合晶体管级仿真结果
%   [opt_rel_error_matrix, opt_adc_output] = OPTIMIZE_ERROR_MODEL(Vin_p, Vin_n, params, transistor_sim_output, sample_indices)
%   使用优化算法查找能够最好地拟合实际仿真数据的相对误差参数
%
% 输入:
%   Vin_p - 差分输入正端波形
%   Vin_n - 差分输入负端波形
%   params - 参数配置结构体
%   transistor_sim_output - 晶体管级仿真的输出数据
%   sample_indices - 采样点索引数组 (已改为使用ideal_sample_indices)
%
% 输出:
%   opt_rel_error_matrix - 优化后的相对误差参数矩阵(num_stages×2)
%   opt_adc_output - 使用优化参数的ADC输出
%
% 注意: 该函数的sample_indices参数现在应该传入ideal_sample_indices

%% 函数验证和初始化
% 验证必要函数是否存在
if ~exist('generate_adaptive_timebase', 'file')
    error('? 错误: 未找到generate_adaptive_timebase函数！\n请确保在调用optimize_error_model之前已正确设置时间基准生成模块。');
end

try
    fprintf('开始执行基于SNR的误差参数优化过程...\n');
    
    % 初始化误差矩阵，使用合理的初始值以加速收敛
    % 一般来说，增益误差和减法误差在高位级较大，低位级较小
    num_stages = params.num_stages;
    init_rel_error_matrix = zeros(num_stages, 2);
    for i = 1:num_stages
        init_rel_error_matrix(i,1) = 0.001 / sqrt(i);  % 初始增益误差
        init_rel_error_matrix(i,2) = 0.001 / sqrt(i);  % 初始减法误差
    end
    
    % 将误差矩阵转换为优化向量
    x0 = init_rel_error_matrix(:);
    
    % 设置优化边界
    lb = -0.2 * ones(size(x0));  % 下界
    ub = 0.2 * ones(size(x0));   % 上界
    
    % 定义目标函数
    objfun = @(x) objective_function(x, Vin_p, Vin_n, params, transistor_sim_output, sample_indices);
    
    % 配置优化器
    options = optimoptions('fmincon', 'Display', 'iter', 'Algorithm', 'sqp', 'MaxIterations', 50);
    
    % 执行fmincon优化
    fprintf('开始执行参数优化...\n');
    [x_opt, fval, exitflag, output] = fmincon(objfun, x0, [], [], [], [], lb, ub, [], options);
    
    % 输出优化结果信息
    fprintf('最终目标函数值: %.6f\n', fval);
    fprintf('优化状态: %d，迭代次数: %d\n', exitflag, output.iterations);
    
    % 将优化结果向量重构为误差参数矩阵
    opt_rel_error_matrix = reshape(x_opt, size(init_rel_error_matrix));
    
    % 使用优化后的参数生成ADC输出
    % 需要生成时钟信号 - 使用新的自适应时间基准
    fprintf('正在生成高精度时钟信号以生成优化后的ADC输出...\n');
    try
        % 使用新的自适应时间基准生成时钟信号
        [~, ~, clks, clkh] = generate_adaptive_timebase(params);
        
        % 使用新的事件驱动流水线ADC模型
        [opt_adc_output, ~, ~, ~] = event_driven_pipeline_adc(Vin_p, Vin_n, params, opt_rel_error_matrix, sample_indices, clks, clkh);
    catch ME
        error('? 无法生成时钟信号或生成优化后的ADC输出: %s\n请检查generate_adaptive_timebase函数调用。', ME.message);
    end
    
    fprintf('误差参数优化完成\n');
    
catch ME
    error('误差模型优化出错: %s', ME.message);
end
end

function obj_value = objective_function(x, Vin_p, Vin_n, params, transistor_sim_output, sample_indices)
% 基于智能采样的增强目标函数
% 采用两阶段处理策略：完整事件驱动处理 + 智能FFT采样
% 输入:
%   x - 误差参数向量（待优化）
%   Vin_p, Vin_n - 差分输入波形数据
%   params - 参数结构
%   transistor_sim_output - 晶体管仿真数据（目标）
%   sample_indices - 采样点索引（兼容性保留）
% 输出:
%   obj_value - 目标函数值(基于智能采样的SNR差异)

    % 使用持久变量记录首次调用，用于输出初始状态
    persistent first_call;
    if isempty(first_call)
        first_call = true;
    end

    try
        % 验证输入参数
        if ~isnumeric(x) || ~isvector(x)
            error('误差参数必须为数值向量');
        end
        
        % 确保参数向量长度与误差矩阵元素数匹配
        num_stages = params.num_stages;
        if length(x) ~= num_stages*2
            error('误差参数向量长度必须等于num_stages*2');
        end
        
        rel_error_matrix = reshape(x, num_stages, 2);

        % 阶段1：完整事件驱动处理
        % 生成完整时钟信号 - 使用新的自适应时间基准
        [t_full, sine_wave, clks, clkh] = generate_adaptive_timebase(params);
        
        % 使用完整156,527个样本运行事件驱动流水线ADC模型
        [adc_output_full, ~, ~, ~] = event_driven_pipeline_adc(...
            Vin_p, Vin_n, params, rel_error_matrix, sample_indices, clks, clkh);

        % 阶段2：智能动态特性采样
        % 从完整波形中智能提取1024个稳定状态样本
        [adc_fft_samples, adc_sampling_info] = extract_dynamic_test_samples(...
            adc_output_full, t_full, clks, clkh, params);
        
        % 同样处理目标仿真数据
        [target_fft_samples, target_sampling_info] = extract_dynamic_test_samples(...
            transistor_sim_output, t_full, clks, clkh, params);

        % 验证采样质量
        min_quality = min(adc_sampling_info.quality_score, target_sampling_info.quality_score);
        if min_quality < 5.0
            % 采样质量过低，返回惩罚值
            obj_value = 1e6 + (5.0 - min_quality) * 1e5;
            return;
        end

        % 阶段3：基于FFT样本计算SNR
        % 计算有效采样率
        fs_effective = calculate_effective_sampling_rate_for_optimization(adc_sampling_info, params);
        
        [~, ~, adc_snr, ~, ~] = calculate_sndr(adc_fft_samples, fs_effective, params.f_in);
        [~, ~, target_snr, ~, ~] = calculate_sndr(target_fft_samples, fs_effective, params.f_in);

        % 计算SNR差异平方（主要优化目标）
        snr_diff_sq = (adc_snr - target_snr)^2;
        
        % 计算波形均方误差（形状匹配度）
        if length(adc_fft_samples) == length(target_fft_samples)
            waveform_mse = mean((adc_fft_samples - target_fft_samples).^2);
        else
            % 长度不匹配时的处理
            min_len = min(length(adc_fft_samples), length(target_fft_samples));
            waveform_mse = mean((adc_fft_samples(1:min_len) - target_fft_samples(1:min_len)).^2);
        end
        
        % 增强目标函数 = SNR差异 + 波形MSE + 采样质量惩罚
        alpha = 1.0;      % SNR差异权重
        beta = 0.1;       % 波形MSE权重  
        gamma = 0.05;     % 采样质量奖励权重
        
        quality_bonus = gamma * adc_sampling_info.quality_score;  % 高质量采样奖励
        
        obj_value = alpha * snr_diff_sq + beta * waveform_mse - quality_bonus;
        
        % 首次调用输出（方便调试）
        if first_call
            fprintf('=== 增强优化目标函数 ===\n');
            fprintf('完整样本数: %d, FFT样本数: %d\n', length(adc_output_full), length(adc_fft_samples));
            fprintf('采样质量: %.1f/10 (模型) %.1f/10 (目标)\n', ...
                    adc_sampling_info.quality_score, target_sampling_info.quality_score);
            fprintf('模型SNR: %.2f dB, 目标SNR: %.2f dB, 差异: %.2f dB\n', ...
                    adc_snr, target_snr, adc_snr - target_snr);
            fprintf('波形MSE: %.6f, 采样质量奖励: %.6f\n', waveform_mse, quality_bonus);
            fprintf('目标函数: %.6f = %.6f(SNR) + %.6f(MSE) - %.6f(质量)\n', ...
                    obj_value, alpha * snr_diff_sq, beta * waveform_mse, quality_bonus);
            first_call = false;
        end
        
    catch ME
        % 出错时输出信息（仅首次）
        if first_call
            fprintf('增强目标函数出错: %s\n', ME.message);
            first_call = false;
        end
        obj_value = 1e6; % 出错时返回大的惩罚值
    end

    % 确保返回有效的目标值
    if isempty(obj_value) || ~isscalar(obj_value) || isnan(obj_value) || isinf(obj_value)
        obj_value = 1e6;
    end
end

function fs_effective = calculate_effective_sampling_rate_for_optimization(sampling_info, params)
%CALCULATE_EFFECTIVE_SAMPLING_RATE_FOR_OPTIMIZATION 优化用的有效采样率计算
%   为优化算法计算准确的有效采样率

    if isfield(sampling_info, 'sample_indices') && length(sampling_info.sample_indices) > 1
        % 基于实际采样点的时间间隔计算
        time_span = max(sampling_info.sample_indices) - min(sampling_info.sample_indices);
        num_samples = length(sampling_info.sample_indices);
        
        % 估算有效采样率
        fs_effective = (num_samples - 1) * params.fs / time_span;
        
        % 限制在合理范围内，避免极端值
        fs_effective = min(fs_effective, params.fs * 1.2);  % 最多超出20%
        fs_effective = max(fs_effective, params.fs / 8);    % 最少为1/8
    else
        % 回退到标准采样率
        fs_effective = params.fs;
    end
end

function pipeline_indices = convert_to_pipeline_indices(hold_indices, ~)
% 将保持态采样点索引转换为流水线采样点索引格式
% 流水线采样点索引格式为采样态/保持态交替排列
% 注意：第二个参数未使用，保留是为了兼容历史调用
    
    % 为简单起见，我们为每个保持态点生成一个虚拟采样态点（直接前移一个点）
    % 实际应用中可能需要更精确的计算
    sample_indices = max(1, hold_indices - 10);  % 采样态点索引，简单前移10个点
    
    % 交替排列采样态和保持态点
    pipeline_indices = zeros(length(hold_indices) * 2, 1);
    for i = 1:length(hold_indices)
        pipeline_indices(2*i-1) = sample_indices(i);  % 采样态点
        pipeline_indices(2*i) = hold_indices(i);      % 保持态点
    end
end 