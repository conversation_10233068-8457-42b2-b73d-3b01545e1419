function extracted_data = extract_pipeline_data(ideal_data_file, sim_data_file)
%   如果未提供文件路径，使用默认路径
if nargin < 1 || isempty(ideal_data_file)
    ideal_data_file = './test_data/pipeline_adc_raw_data.mat';
end
if nargin < 2 || isempty(sim_data_file)
    sim_data_file = './test_data/Analog_processing_data.mat';
end
%EXTRACT_PIPELINE_DATA 提取流水线ADC数据用于理想模型与晶体管仿真对比
%   VERSION: v1.0 - 时钟同步数据提取系统
%
%   功能描述:
%   从理想流水线ADC模型数据和Cadence Virtuoso晶体管仿真数据中提取
%   时钟同步的采样点，用于精确的性能对比分析
%
%   核心算法:
%   1. 晶体管仿真数据(非均匀采样): 检测时钟下降沿，在下降沿前第10个样本点提取
%   2. 理想模型数据(均匀采样): 每1000个样本点对应一个采样周期
%   3. 时钟域分离: CLKH域(奇数级) vs CLKS域(偶数级+SHA)
%   4. 数据对齐: 跳过前19个周期，提取第20-119周期的数据
%
%   输入参数:
%       ideal_data_file - 理想模型数据文件路径 (.mat格式)
%       sim_data_file - 晶体管仿真数据文件路径 (.mat格式)
%
%   输出参数:
%       extracted_data - 提取后的结构化数据
%           .SHA.ideal_signal, .SHA.sim_signal - SHA模块对比数据
%           .STAGE1~8.ideal_signal, .STAGE1~8.sim_signal - 各级对比数据
%           .extraction_info - 提取过程统计信息

    fprintf('=== 流水线ADC数据提取系统 ===\n');
    
    %% 参数设置
    skip_cycles = 20;          % 跳过前20个采样周期（预热期）
    extract_cycles = 100;      % 提取100个采样周期的数据
    samples_per_cycle = 1000;  % 理想模型：每个采样周期1000个样本点
    stable_offset = 10;        % 在时钟下降沿前第10个样本点提取稳定数据
    
    %% 初始化输出结构体
    extracted_data = struct();
    stage_names = {'SHA', 'STAGE1', 'STAGE2', 'STAGE3', 'STAGE4', ...
                   'STAGE5', 'STAGE6', 'STAGE7', 'STAGE8'};
    
    for i = 1:length(stage_names)
        extracted_data.(stage_names{i}) = struct();
        extracted_data.(stage_names{i}).ideal_signal = [];
        extracted_data.(stage_names{i}).sim_signal = [];
    end
    
    %% 第一步：数据加载
    fprintf('\n[步骤1/2] 数据加载...\n');
    
    % 加载理想模型数据
    fprintf('  正在加载理想模型数据...\n');
    if ~exist(ideal_data_file, 'file')
        error('理想模型数据文件不存在: %s', ideal_data_file);
    end
    
    try
        ideal_data = load(ideal_data_file);
    catch ME
        error('理想模型数据文件读取失败: %s', ME.message);
    end
    
    % 检查数据结构并适配不同的数据格式
    if isfield(ideal_data, 'pipeline_raw_data')
        raw_data = ideal_data.pipeline_raw_data;
        fprintf('    使用标准格式: pipeline_raw_data\n');
    else
        % 尝试其他可能的字段名
        field_names = fieldnames(ideal_data);
        if length(field_names) == 1
            raw_data = ideal_data.(field_names{1});
            fprintf('    使用字段: %s\n', field_names{1});
        else
            error('理想模型数据格式错误：无法确定数据结构，可用字段: %s', ...
                  strjoin(field_names, ', '));
        end
    end
    
    % 检查必要的数据字段
    if ~isfield(raw_data, 'analog_data_struct')
        error('理想模型数据格式错误：缺少analog_data_struct字段，可用字段: %s', ...
              strjoin(fieldnames(raw_data), ', '));
    end
    if ~isfield(raw_data, 'time_vector')
        error('理想模型数据格式错误：缺少time_vector字段，可用字段: %s', ...
              strjoin(fieldnames(raw_data), ', '));
    end
    
    ideal_time = raw_data.time_vector;
    ideal_analog = raw_data.analog_data_struct;
    
    fprintf('    理想模型数据加载成功 - 总样本数: %d, 模拟信号数: %d\n', ...
            length(ideal_time), length(ideal_analog.signals));
    
    % 加载晶体管仿真数据
    fprintf('  正在加载晶体管仿真数据...\n');
    if ~exist(sim_data_file, 'file')
        error('晶体管仿真数据文件不存在: %s', sim_data_file);
    end
    
    try
        sim_data = load(sim_data_file);
    catch ME
        error('晶体管仿真数据文件读取失败: %s', ME.message);
    end
    
    % 检查仿真数据结构并适配不同格式
    if isfield(sim_data, 'simData') && isfield(sim_data.simData, 'signals')
        sim_signals = sim_data.simData.signals;
        fprintf('    使用标准格式: simData.signals\n');
    elseif isfield(sim_data, 'signals')
        sim_signals = sim_data.signals;
        fprintf('    使用简化格式: signals\n');
    else
        % 尝试查找其他可能的信号数据字段
        field_names = fieldnames(sim_data);
        found_signals = false;
        for i = 1:length(field_names)
            field_data = sim_data.(field_names{i});
            if isstruct(field_data) && isfield(field_data, 'signals')
                sim_signals = field_data.signals;
                fprintf('    使用字段: %s.signals\n', field_names{i});
                found_signals = true;
                break;
            end
        end
        if ~found_signals
            error('晶体管仿真数据格式错误：无法找到signals字段，可用字段: %s', ...
                  strjoin(field_names, ', '));
        end
    end
    
    fprintf('    晶体管仿真数据加载成功 - 信号通道数: %d\n', length(sim_signals));
    
    % 列出所有信号通道并验证信号结构
    fprintf('    信号通道列表:\n');
    if ~isempty(sim_signals) && isstruct(sim_signals)
        for i = 1:min(length(sim_signals), 10)  % 最多显示前10个信号
            if isfield(sim_signals(i), 'label')
                fprintf('      %d: %s\n', i, sim_signals(i).label);
            else
                fprintf('      %d: [无标签字段]\n', i);
            end
        end
        if length(sim_signals) > 10
            fprintf('      ... 还有 %d 个信号\n', length(sim_signals) - 10);
        end
    else
        error('信号数据结构异常：sim_signals 不是有效的结构体数组');
    end
    
    fprintf('✓ 数据加载完成\n');
    
    %% 第二步：数据提取
    fprintf('\n[步骤2/2] 数据提取...\n');
    
    % 提取理想模型数据（均匀采样）
    fprintf('  正在提取理想模型数据（均匀采样）...\n');
    
    % 计算起始索引：第20个周期的开始
    start_sample = skip_cycles * samples_per_cycle + 1;
    
    % 验证数据长度
    total_needed_samples = (skip_cycles + extract_cycles) * samples_per_cycle;
    if length(ideal_time) < total_needed_samples
        error('理想模型数据不足：需要%d样本，实际%d样本', ...
              total_needed_samples, length(ideal_time));
    end
    
    % 计算提取索引：在每个周期的固定位置提取
    % 假设在每个周期的第990个样本点提取（相当于下降沿前第10个点）
    stable_position = samples_per_cycle - stable_offset;
    
    ideal_indices = [];
    for cycle = 0:(extract_cycles-1)
        cycle_start = start_sample + cycle * samples_per_cycle;
        extract_idx = cycle_start + stable_position - 1;  % -1因为MATLAB索引从1开始
        if extract_idx > length(ideal_time)
            error('理想模型提取索引超出数据范围：索引%d，数据长度%d', extract_idx, length(ideal_time));
        end
        ideal_indices(end+1) = extract_idx;
    end
    
    fprintf('    理想模型提取索引计算完成 - 起始样本: %d, 提取样本数: %d\n', ...
            start_sample, length(ideal_indices));
    
    % 提取各级模拟信号
    if length(ideal_analog.signals) < 9
        error('理想模型信号数量不足：需要9个信号，实际%d个', length(ideal_analog.signals));
    end
    
    for stage_idx = 1:9
        stage_signal = ideal_analog.signals(stage_idx).values;
        if length(stage_signal) < max(ideal_indices)
            error('理想模型第%d级信号长度不足：需要%d，实际%d', ...
                  stage_idx, max(ideal_indices), length(stage_signal));
        end
        extracted_signal = stage_signal(ideal_indices);
        
        stage_name = stage_names{stage_idx};
        extracted_data.(stage_name).ideal_signal = extracted_signal;
    end
    
    fprintf('    理想模型数据提取完成\n');
    
    % 提取晶体管仿真数据（非均匀采样）
    fprintf('  正在提取晶体管仿真数据（非均匀采样）...\n');
    
    % 查找时钟信号
    clkh_signal = [];
    clks_signal = [];
    sim_time = [];
    
    for i = 1:length(sim_signals)
        if isfield(sim_signals(i), 'label') && isfield(sim_signals(i), 'values')
            if contains(sim_signals(i).label, 'CLKH_V_')
                clkh_signal = sim_signals(i).values;
                if isfield(sim_signals(i), 'time')
                    sim_time = sim_signals(i).time;
                end
                fprintf('    找到CLKH信号: %s\n', sim_signals(i).label);
            elseif contains(sim_signals(i).label, 'CLKS_V_')
                clks_signal = sim_signals(i).values;
                fprintf('    找到CLKS信号: %s\n', sim_signals(i).label);
            end
        end
    end
    
    if isempty(clkh_signal) || isempty(clks_signal)
        error('无法找到时钟信号CLKH_V_或CLKS_V_');
    end
    
    % 检测时钟下降沿
    [clkh_falling_edges] = detect_falling_edges(clkh_signal, sim_time);
    [clks_falling_edges] = detect_falling_edges(clks_signal, sim_time);
    
    fprintf('    检测到CLKH下降沿: %d个, CLKS下降沿: %d个\n', ...
            length(clkh_falling_edges), length(clks_falling_edges));
    
    % 验证下降沿数量
    min_required_edges = skip_cycles + extract_cycles;
    if length(clkh_falling_edges) < min_required_edges || length(clks_falling_edges) < min_required_edges
        error('时钟下降沿数量不足：需要%d个，CLKH有%d个，CLKS有%d个', ...
              min_required_edges, length(clkh_falling_edges), length(clks_falling_edges));
    end
    
    % 定义时钟域分配
    clkh_stages = {'STAGE1', 'STAGE3', 'STAGE5', 'STAGE7'};  % CLKH控制的模块
    clks_stages = {'SHA', 'STAGE2', 'STAGE4', 'STAGE6', 'STAGE8'};  % CLKS控制的模块
    
    % 提取各级仿真信号
    for stage_idx = 1:length(stage_names)
        stage_name = stage_names{stage_idx};
        
        % 确定该级使用的时钟
        if ismember(stage_name, clkh_stages)
            falling_edges = clkh_falling_edges;
            clock_type = 'CLKH';
        else
            falling_edges = clks_falling_edges;
            clock_type = 'CLKS';
        end
        
        % 查找对应的仿真信号
        sim_signal_data = [];
        signal_label = '';
        
        if strcmp(stage_name, 'SHA')
            signal_label = 'SHA_V_';
        else
            signal_label = sprintf('%s_V_', stage_name);
        end
        
        for i = 1:length(sim_signals)
            if isfield(sim_signals(i), 'label') && isfield(sim_signals(i), 'values')
                if contains(sim_signals(i).label, signal_label)
                    sim_signal_data = sim_signals(i).values;
                    fprintf('    找到%s信号: %s (时钟域: %s)\n', stage_name, sim_signals(i).label, clock_type);
                    break;
                end
            else
                if i == 1  % 只在第一次时警告
                    fprintf('    警告: 信号%d缺少label或values字段\n', i);
                end
            end
        end
        
        if isempty(sim_signal_data)
            % 提供更详细的错误信息，列出可用的信号标签
            available_labels = {};
            for j = 1:length(sim_signals)
                if isfield(sim_signals(j), 'label')
                    available_labels{end+1} = sim_signals(j).label;
                end
            end
            error('未找到%s的仿真信号（搜索标签: %s）\n可用信号: %s', ...
                  stage_name, signal_label, strjoin(available_labels, ', '));
        end
        
        % 在时钟下降沿前第10个样本点提取数据
        extracted_signal = extract_stable_samples(sim_signal_data, sim_time, ...
                                                 falling_edges, skip_cycles, ...
                                                 extract_cycles, stable_offset);
        
        extracted_data.(stage_name).sim_signal = extracted_signal;
    end
    
    fprintf('    晶体管仿真数据提取完成\n');
    
    %% 添加提取统计信息
    extracted_data.extraction_info = struct();
    extracted_data.extraction_info.skip_cycles = skip_cycles;
    extracted_data.extraction_info.extract_cycles = extract_cycles;
    extracted_data.extraction_info.samples_per_cycle = samples_per_cycle;
    extracted_data.extraction_info.stable_offset = stable_offset;
    extracted_data.extraction_info.total_extracted_samples = extract_cycles;
    extracted_data.extraction_info.clkh_stages = clkh_stages;
    extracted_data.extraction_info.clks_stages = clks_stages;
    
    fprintf('✓ 数据提取完成\n');
    
    fprintf('\n=== 数据提取完成 ===\n');
    fprintf('提取统计:\n');
    fprintf('  跳过周期: %d\n', skip_cycles);
    fprintf('  提取周期: %d\n', extract_cycles);
    fprintf('  每个模块提取样本数: %d\n', extract_cycles);
    
    % 验证数据长度一致性
    fprintf('\n数据长度验证:\n');
    for i = 1:length(stage_names)
        stage_name = stage_names{i};
        ideal_len = length(extracted_data.(stage_name).ideal_signal);
        sim_len = length(extracted_data.(stage_name).sim_signal);
        fprintf('  %s: 理想%d, 仿真%d', stage_name, ideal_len, sim_len);
        if ideal_len == sim_len
            fprintf(' ✓\n');
        else
            error('%s模块数据长度不匹配：理想%d，仿真%d', stage_name, ideal_len, sim_len);
        end
    end
end

function falling_edges = detect_falling_edges(signal, time_vector)
%DETECT_FALLING_EDGES 检测信号的下降沿时间索引
%   检测数字时钟信号的下降沿（从高电平到低电平的转换）
%   
%   算法：寻找相邻样本点中 signal(i-1) > threshold 且 signal(i) <= threshold 的位置

    threshold = 0.5;  % 时钟信号的阈值（假设为0.5V）
    falling_edges = [];
    
    for i = 2:length(signal)
        if signal(i-1) > threshold && signal(i) <= threshold
            falling_edges(end+1) = i;
        end
    end
    
    fprintf('      检测到%d个下降沿\n', length(falling_edges));
end

function extracted_signal = extract_stable_samples(signal_data, time_vector, ...
                                                  falling_edges, skip_cycles, ...
                                                  extract_cycles, stable_offset)
%EXTRACT_STABLE_SAMPLES 在时钟下降沿前提取稳定的信号样本
%   在每个时钟下降沿前第stable_offset个样本点提取数据
%   确保提取的是稳定状态下的信号值

    extracted_signal = [];
    
    % 从第(skip_cycles+1)个下降沿开始提取
    start_edge_idx = skip_cycles + 1;
    end_edge_idx = skip_cycles + extract_cycles;
    
    for edge_idx = start_edge_idx:min(end_edge_idx, length(falling_edges))
        falling_idx = falling_edges(edge_idx);
        
        % 在下降沿前第stable_offset个样本点提取
        extract_idx = falling_idx - stable_offset;
        
        if extract_idx <= 0
            error('提取索引超出范围：下降沿%d，计算索引%d <= 0', edge_idx, extract_idx);
        end
        
        if extract_idx > length(signal_data)
            error('提取索引超出范围：下降沿%d，计算索引%d > 数据长度%d', ...
                  edge_idx, extract_idx, length(signal_data));
        end
        
        extracted_signal(end+1) = signal_data(extract_idx);
    end
    
    % 确保返回列向量
    extracted_signal = extracted_signal(:);
end 